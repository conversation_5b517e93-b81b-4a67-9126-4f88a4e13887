import json
import os
from typing import Dict, Any, List

class ConfigManager:
    def __init__(self):
        self.config_file = "config.json"
        self.default_config = {
            "model_services": [],
            "default_service": None,
            "default_model": None,
            "characters": [],
            "settings": {
                "save_directory": "D:\\BaiduSyncdisk\\Youtube\\00待剪辑",
                "timeout": 180,
                "retry_count": 3,
                "prevent_overwrite": True,
                "add_timestamp": True,
                "filename_format": "timestamp"
            }
        }
        self.load_config()
    
    def load_config(self):
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            except Exception:
                self.config = self.default_config.copy()
        else:
            self.config = self.default_config.copy()
    
    def save_config(self):
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
    
    def get_model_config(self) -> Dict[str, Any]:
        return {
            "services": self.config.get("model_services", []),
            "default_service": self.config.get("default_service"),
            "default_model": self.config.get("default_model"),
            "settings": self.config.get("settings", self.default_config["settings"])
        }
    
    def save_model_config(self, config_data: Dict[str, Any]):
        if "services" in config_data:
            self.config["model_services"] = config_data["services"]
        if "default_service" in config_data:
            self.config["default_service"] = config_data["default_service"]
        if "default_model" in config_data:
            self.config["default_model"] = config_data["default_model"]
        if "settings" in config_data:
            self.config["settings"].update(config_data["settings"])
        self.save_config()
    
    def add_model_service(self, service_data: Dict[str, Any]):
        services = self.config.get("model_services", [])
        
        # 检查名称唯一性
        for service in services:
            if service.get("name") == service_data.get("name"):
                raise ValueError(f"Service name '{service_data['name']}' already exists")
        
        # 生成新的ID，确保唯一性
        existing_ids = [int(service.get("id", 0)) for service in services if str(service.get("id", "")).isdigit()]
        service_id = max(existing_ids) + 1 if existing_ids else 1
        service_data["id"] = service_id
        
        services.append(service_data)
        self.config["model_services"] = services
        self.save_config()
    
    def update_model_service(self, service_id: int, service_data: Dict[str, Any]):
        services = self.config.get("model_services", [])
        for i, service in enumerate(services):
            if service.get("id") == service_id:
                services[i] = {**service, **service_data}
                break
        self.save_config()
    
    def delete_model_service(self, service_id: int):
        services = self.config.get("model_services", [])
        self.config["model_services"] = [s for s in services if s.get("id") != service_id]
        self.save_config()
    
    def get_characters(self) -> List[Dict[str, Any]]:
        """获取角色配置，支持新旧数据格式"""
        characters = self.config.get("characters", [])

        # 检查是否需要数据迁移（从旧格式转换为新格式）
        if characters and self._needs_migration(characters):
            characters = self._migrate_characters_data(characters)
            self.save_characters(characters)

        return characters

    def save_characters(self, characters: List[Dict[str, Any]]):
        self.config["characters"] = characters
        self.save_config()

    def _needs_migration(self, characters: List[Dict[str, Any]]) -> bool:
        """检查是否需要数据迁移"""
        if not characters:
            return False

        # 检查第一个角色是否使用旧格式（有url字段但没有outfits字段）
        first_char = characters[0]
        return "url" in first_char and "outfits" not in first_char

    def _migrate_characters_data(self, old_characters: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """将旧格式的角色数据迁移到新格式"""
        from datetime import datetime

        new_characters = []
        character_groups = {}

        # 按角色编码分组
        for old_char in old_characters:
            code = old_char.get("code", "")
            if not code:
                continue

            # 提取基础角色编码（去除可能的变体后缀）
            base_code = self._extract_base_character_code(code)

            if base_code not in character_groups:
                character_groups[base_code] = {
                    "character_code": base_code,
                    "character_name": old_char.get("name", ""),
                    "core_features": old_char.get("core_features", ""),
                    "display_type": old_char.get("display_type", "name"),
                    "outfits": [],
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }

            # 添加服装
            if old_char.get("url"):
                outfit = {
                    "outfit_id": f"outfit_{len(character_groups[base_code]['outfits']) + 1}",
                    "outfit_name": self._generate_outfit_name(old_char.get("code", ""), base_code),
                    "outfit_description": "",
                    "image_url": old_char.get("url", ""),
                    "created_at": datetime.now().isoformat()
                }
                character_groups[base_code]["outfits"].append(outfit)

        # 转换为列表格式
        new_characters = list(character_groups.values())

        return new_characters

    def _extract_base_character_code(self, full_code: str) -> str:
        """提取基础角色编码"""
        # 移除常见的变体后缀
        suffixes = [" Baby", " Child", " Adult", " Old", "_v1", "_v2", "_variant"]
        base_code = full_code

        for suffix in suffixes:
            if base_code.endswith(suffix):
                base_code = base_code[:-len(suffix)]
                break

        return base_code

    def _generate_outfit_name(self, full_code: str, base_code: str) -> str:
        """根据完整编码生成服装名称"""
        if full_code == base_code:
            return "默认服装"

        # 提取变体部分作为服装名称
        variant = full_code.replace(base_code, "").strip()
        if variant.startswith(" "):
            variant = variant[1:]
        if variant.startswith("_"):
            variant = variant[1:]

        return variant if variant else "服装变体"
    
    def export_config(self) -> Dict[str, Any]:
        return self.config.copy()
    
    def import_config(self, config_data: Dict[str, Any]):
        self.config = config_data
        self.save_config()
    
    def get_setting(self, key: str, default=None):
        return self.config.get("settings", {}).get(key, default)
    
    def set_setting(self, key: str, value: Any):
        if "settings" not in self.config:
            self.config["settings"] = {}
        self.config["settings"][key] = value
        self.save_config()