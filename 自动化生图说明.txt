帮我写个自动化抽图的Python程序，前端HTML使用现代化的UI，使用 ES 模块 (import/export) 语法，而非 CommonJS (require)，尽可能解构导入 (例如 import { foo } from 'bar')。实现以下需求：
1. 通用的模型服务配置：可以添加多个自定义的OpenAI类的模型服务，设置API密钥、地址，添加模型、接口超时时间配置（有时候接口响应比较慢，默认超时时间3分钟）、失败重试次数（默认3次）。可以设置默认的模型服务和模型。模型主要分两种：对话模型（当前暂未使用到）、生图模型。
2. 角色参考图配置：可以配置多个角色，每个角色可以配置角色唯一编码、角色名、角色url（配置了角色url后，需要渲染一个小图出来，点击小图可以放大）
3. 各种配置都要能持久化保存，可以导入导出。
4. 【一键生成故事】功能模块：用户导入Excel文件，读取Excel文件的"镜头序号"和"文生图prompt"列，并渲染出来，再根据配置，去调用选择的模型服务和模型，然后生成图片，保存到指定目录中。详细说明如下：
a. 最上方为功能区："选择生图Excel文件"、选择模型服务（默认值是通用的模型服务配置的默认的模型服务）、选择生图模型（默认值是通用的模型服务配置的默认的生图模型）、每个分镜批量生图数量（输入数值后，旁边要有个确认按钮，确认后这个数量则应用到下方所有分镜的生图数量中）、设置保存目录（默认D:\BaiduSyncdisk\Youtube\wcs_util目录）、导出Excel文件（为什么要有这个功能，因为渲染后的Excel，多行的文本框中的值即"文生图prompt"列中的值可能会修改，导出后就是导出修改后"文生图prompt"列的值和"镜头序号"）、一键生成所有分镜
b. 用户选择导入Excel文件后，读取Excel文件的"镜头序号"和"文生图prompt"列，并渲染出来。
c. 渲染的UI说明示例：一个分镜一个框。
左边显示"分镜1"，值来自于"镜头序号"列；
左边下方有个复选框，默认勾选（这个是用于：一键生成所有分镜的，如果未勾选，则一键生成所有分镜的时候，忽略该未勾选的分镜）
中间是一个多行的文本框，值默认取自"文生图prompt"列中的值，可以编辑、修改；
中间下方可以【选择参考角色】，作为生成图片的角色垫图，最多选择5张角色垫图；
右边是：【生图数量】，默认1，可以被上方的"每个分镜批量生图数量"统一修改，也可以单独修改数量；
右边下面有个【生成图片】按钮，点击这个按钮，即调用对应的模型服务的生图模型单独生成图片，并把图片保存到设置的保存目录中。单个生图的接口的返回值有多个图片的url，所以单次的生图接口可能有多张图片。所以图片命名为excel文件名+镜头名+自增的数值，需要有个自增序号，生图数量为1的时候示例："冰块_镜头1_1.png"，"冰块_镜头1_2.png"（生图接口生成了两张图），生图数量为2的时候示例："冰块_镜头1_1.png"、"冰块_镜头1_2.png"、"冰块_镜头1_3.png"（两次生图接口生成了三张图）。
d. 其他分镜按顺序列下来，UI与c中描述的一致。
5. 【选择参考角色】功能具体说明：即选择【角色参考图配置】中的角色，选择后，渲染对应角色的小图，点击小图可以放大。实际作为【生成图片】接口的参数传参时，传的是角色url。如果选择了角色，但角色url为空，则需要弹框提醒。
6. 【生成图片】调用的接口具体说明：
a. 含参考图的情况，入参参考【生成图片接口请求入参（含参考图）】，text 的值由【接口参考图说明】和【多行的文本框中的值】组成。【参考图说明】根据【选择参考角色】生成，比如只选择了一个参考角色的时候，选择的角色名为"木头人"，旁边默认的参考图说明为"木头人的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。"，这个说明是一个文本框，木头人是动态的取自角色名，第一张图片也是动态的即第一张参考图。文本框还可以自定义修改，【接口参考图说明】的值为=参考图说明的值（如果编辑了，则是编辑后的值），角色url对应替换image_url中的【参考角色1】。选择多个参考角色的时候，第一个参考角色选择的角色名为"木头人"，旁边默认的参考图说明为"木头人的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。"，第二个参考角色选择的角色名为"卡布奇诺咖啡女"，旁边默认的参考图说明为"卡布奇诺咖啡女的角色形象:请严格参考我提供的第二张图片(角色参考图)来塑造角色。"。木头人、卡布奇诺咖啡女都是动态取自角色名，第一张图片、第二张图片即对应的第几张参考图。【接口参考图说明】的值为="木头人的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。卡布奇诺咖啡女的角色形象:请严格参考我提供的第二张图片(角色参考图)来塑造角色。"，角色url对应替换image_url中的【参考角色1】、【参考角色2】。其实多个参考角色，就是多个角色的参考图说明拼接起来作为【接口参考图说明】的值。
b. 不含参考图的情况，text的值直接=【多行的文本框中的值】，入参参考：【生成图片接口请求入参（不含参考图））】
c. 接口出参参考：【生成图片接口请求出参】，注意图片接口出参中有多个图片url，他们虽然url不同，但是有时候图片内容是一样的，要做去重处理。图片的格式主要有两种，一种是"https://videos.openai.com/vg-assets/assets%2Ftask_01k06d1z7cftstwwm2e8p8wgnt%2F1752561872_src_0.png?st=2025-07-15T05%3A42%3A56Z&se=2025-07-21T06%3A42%3A56Z&sks=b&skt=2025-07-15T05%3A42%3A56Z&ske=2025-07-21T06%3A42%3A56Z&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skoid=aa5ddad1-c91a-4f0a-9aca-e20682cc8969&skv=2019-02-02&sv=2018-11-09&sr=b&sp=r&spr=https%2Chttp&sig=sZNuhNjf9fQXHQAabZ%2FvGPwrQj6hthpoxOE4W5v1t30%3D&az=oaivgprodscus"这种，一种是"https://filesystem.site/cdn/20250715/YQmg5H33EGZsyO2xoaIjzpPWg1C4B5.png"
7. 【生图数量】功能具体说明：就是相同的入参重复调用接口，因为生图的结果是不稳定的，可以通过这个数量来批量抽图
8. 【一键生成所有分镜】功能具体说明：前提是导入了对应的分镜的Excel了，然后批量并发调用生成图片接口。比如25个分镜，每个分镜的生图数量为2，则批量调用50个接口（25个不同的接口，每个接口有一次重复调用）
9. 生成的图片要回显到对应的镜头下方，用小图显示，点击可以放大。
10. 【一键生成故事】功能模块只是当前的一个模块，后续可能会新增其他模块功能，所以要提前预留好。
11. 要完善的日志模块，每一步操作都要记录日志，每一步简单的日志显示在页面的右侧，页面右侧单独划分一小块页面用来显示操作日志。
12. 详细的日志同时需要写入到日志.txt中，对于并发调用接口的操作，为避免日志混淆，还需要添加一个唯一的traceId用于区分。日志内容为：处理的excel文件路径、处理的结果、处理的时间、调用openai接口的入参出参等尽可能完善的日志。
13. 需要有处理进度条，显示处理的进度，并根据接口调用时间预估完成时间
14. 完善的异常处理功能

生成图片接口请求入参（含参考图）="""
{
    "model": "【生图模型】",
    "messages": [
			{
			  "role": "user",
			  "content": [
                  {"type":"text","text":"【接口参考图说明】"+"【多行的文本框中的值】"},
                  {"type":"image_url","image_url":{
                      "url": "【参考角色1】"
                    }
                  },
                  {"type":"image_url","image_url":{
                      "url": "【参考角色2】"
                    }
                  },
                  {"type":"image_url","image_url":{
                      "url": "【参考角色3】"
                    }
                  },
                  {"type":"image_url","image_url":{
                      "url": "【参考角色4】"
                    }
                  }
                ]
			}
    ],
    "stream": false
}

"""


生成图片接口请求入参（不含参考图）="""
{
    "model": "【生图模型】",
    "messages": [
			{
			  "role": "user",
			  "content": [
                  {"type":"text","text":"【多行的文本框中的值】"}
                ]
			}
    ],
    "stream": false
}

"""

生成图片接口请求出参="""
  {
    "id": "chatcmpl-89DRyLhKzQN7VOenCYlNniokXdYcO",
    "object": "chat.completion",
    "created": 1752561879,
    "model": "sora_image",
    "choices": [
      {
        "index": 0,
        "message": {
          "role": "assistant",
          "content": "```json\n{\n  \"prompt\": \"室内咖啡厅，靠窗高桌，远处顾客悠闲品咖，透过巨幅落地窗可见略微朦胧的街景与建筑，背景整体虚化形成浅景深；整个躯干与头部是一根光滑的木质棒球棒，棒身上部直接雕刻出眼睛、鼻子、嘴巴；右手始终握着木质棒球棒；单份忠实复刻上述淡棕木色木质感杯身、左侧女性手悬空自然持杯、高脚透明玻璃杯杯型、杯内约三分之二蜜桃棕木色气泡水填满晶莹冰块、杯口覆盖云朵般浓厚甜奶油泡、杯沿挂一片新鲜柠檬片并斜插一根精致棕木色吸管、杯壁贴有异形贴纸的细节的融合；\\\"Tung Tung Tung Sahur\\\"站立于奶油云朵顶部，双足微陷奶油中，右手握木质棒球棒，人物与饮料高度一体化，可爱且逼真；“Tung Tung Tung Sahur”手写艺术体标识印于杯前异形贴纸，贴纸背景为棕木色，贴纸图案为同款Tung Tung Tung Sahur卡通形象，整体呈现品牌限定饮品；主色：棕木色、奶油白、淡棕木桃色；点缀柠檬黄；Photorealistic, 8K, shallow depth of field, cinematic lighting, whimsical，专业级美食与角色混合摄影风格；柔和窗边自然光与顶部暖调补光共同照亮场景，玻璃杯与冰块呈现通透高光，整体光线明亮甜美\",\n  \"ratio\": \"9:16\",\n  \"n\": 1\n}\n```\n\n>🕐 排队中.\n\n>⚡ 生成中......\n\n>🏃‍ 进度 1....4.....11.....15....19.......26....29....34.....38.....42....47.....51....55.....59....63.....67....[100](https://videos.openai.com/vg-assets/assets%2Ftask_01k06d1z7cftstwwm2e8p8wgnt%2F1752561872_src_0.png?st=2025-07-15T05%3A42%3A56Z&se=2025-07-21T06%3A42%3A56Z&sks=b&skt=2025-07-15T05%3A42%3A56Z&ske=2025-07-21T06%3A42%3A56Z&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skoid=aa5ddad1-c91a-4f0a-9aca-e20682cc8969&skv=2019-02-02&sv=2018-11-09&sr=b&sp=r&spr=https%2Chttp&sig=sZNuhNjf9fQXHQAabZ%2FvGPwrQj6hthpoxOE4W5v1t30%3D&az=oaivgprodscus)\n\n> ✅ 生成完成\n\n\n![gen_01k06d20y0fthvyxbdjc0w13e6](https://filesystem.site/cdn/20250715/YQmg5H33EGZsyO2xoaIjzpPWg1C4B5.png)\n\n[点击下载](https://filesystem.site/cdn/download/20250715/YQmg5H33EGZsyO2xoaIjzpPWg1C4B5.png)"
        },
        "finish_reason": "stop"
      }
    ],
    "usage": {
      "prompt_tokens": 1017,
      "completion_tokens": 1081,
      "total_tokens": 2098,
      "prompt_tokens_details": {
        "text_tokens": 1010
      },
      "completion_tokens_details": {
        "content_tokens": 1081
      }
    }
  }
"""

