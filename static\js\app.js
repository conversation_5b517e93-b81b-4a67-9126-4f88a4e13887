class App {
    constructor() {
        this.currentExcelData = null;
        this.currentScenes = [];
        this.selectedCharacters = {};
        this.isGenerating = false;
        this.characters = []; // 初始化角色数组
        this.modelServices = []; // 初始化模型服务数组

        // Excel编辑器相关数据
        this.excelEditorData = null;
        this.originalExcelData = null;
        this.excelEditorFileName = '';

        this.init();
    }

    init() {
        this.bindEvents();
        this.loadConfigs();
        this.initTabs();
        this.loadSavedData(); // 加载保存的数据
    }

    bindEvents() {
        // 标签页切换
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 文件选择
        document.getElementById('selectExcelBtn').addEventListener('click', () => {
            document.getElementById('excelFile').click();
        });

        document.getElementById('excelFile').addEventListener('change', (e) => {
            this.handleExcelUpload(e.target.files[0]);
        });

        // 模型服务选择
        document.getElementById('modelService').addEventListener('change', (e) => {
            this.loadModelsForService(e.target.value);
        });

        // 批量设置生图数量
        document.getElementById('applyBatchCount').addEventListener('click', () => {
            this.applyBatchCount();
        });

        // 批量设置核心特征开关
        document.getElementById('applyCoreFeatures').addEventListener('click', () => {
            this.applyCoreFeaturesToAll();
        });

        // 一键生成所有分镜
        document.getElementById('generateAllBtn').addEventListener('click', () => {
            this.generateAllScenes();
        });

        // 导出Excel
        document.getElementById('exportExcelBtn').addEventListener('click', () => {
            this.exportExcel();
        });

        // 保存缓存
        document.getElementById('saveCacheBtn').addEventListener('click', () => {
            this.manualSaveCache();
        });

        // 清空数据
        document.getElementById('clearDataBtn').addEventListener('click', () => {
            this.clearAllData();
        });

        // 图床相关事件
        document.getElementById('saveImageHostBtn').addEventListener('click', () => {
            this.saveImageHostConfig();
        });

        // 保存设置相关事件
        document.getElementById('saveSaveSettingsBtn').addEventListener('click', () => {
            this.saveSaveSettings();
        });

        // 保存设置变化事件
        document.getElementById('preventOverwriteCheck').addEventListener('change', () => {
            this.updateFilenamePreview();
        });

        document.getElementById('addTimestampCheck').addEventListener('change', () => {
            this.updateFilenamePreview();
        });

        document.getElementById('filenameFormatSelect').addEventListener('change', () => {
            this.updateFilenamePreview();
        });

        document.getElementById('uploadArea').addEventListener('click', () => {
            document.getElementById('imageFile').click();
        });

        document.getElementById('imageFile').addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files[0]);
        });

        // 历史记录相关事件
        document.getElementById('refreshHistoryBtn').addEventListener('click', () => {
            this.loadUploadHistory();
        });

        document.getElementById('clearHistoryBtn').addEventListener('click', () => {
            this.clearUploadHistory();
        });

        // 拖拽上传
        const uploadArea = document.getElementById('uploadArea');
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileSelect(files[0]);
            }
        });

        // 角色形象生成相关事件
        document.getElementById('addCharGenPromptBtn').addEventListener('click', () => {
            this.addCharacterGenPrompt();
        });

        document.getElementById('charGenGenerateAllBtn').addEventListener('click', () => {
            this.generateAllCharacterImages();
        });

        document.getElementById('charGenSaveCacheBtn').addEventListener('click', () => {
            this.saveCharGenCache();
        });

        document.getElementById('charGenClearDataBtn').addEventListener('click', () => {
            this.clearCharGenData();
        });

        document.getElementById('charGenModelService').addEventListener('change', (e) => {
            this.loadCharGenModelsForService(e.target.value);
        });

        document.getElementById('applyCharGenImageCountBtn').addEventListener('click', () => {
            this.applyCharGenImageCountToAll();
        });

        document.getElementById('charGenBrowseDirBtn').addEventListener('click', () => {
            this.browseCharGenDirectory();
        });

        // Excel转换相关事件
        document.getElementById('txtUploadArea').addEventListener('click', () => {
            document.getElementById('txtFile').click();
        });

        document.getElementById('txtFile').addEventListener('change', (e) => {
            this.handleTxtFileSelect(e.target.files[0]);
        });

        document.getElementById('excelBrowseDirBtn').addEventListener('click', () => {
            this.browseExcelDirectory();
        });

        document.getElementById('clearExcelDataBtn').addEventListener('click', () => {
            this.clearExcelData();
        });

        document.getElementById('downloadExcelBtn').addEventListener('click', () => {
            this.downloadGeneratedExcel();
        });

        document.getElementById('openExcelDirBtn').addEventListener('click', () => {
            this.openExcelDirectory();
        });

        // TXT文件拖拽上传
        const txtUploadArea = document.getElementById('txtUploadArea');
        txtUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            txtUploadArea.classList.add('dragover');
        });

        txtUploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            txtUploadArea.classList.remove('dragover');
        });

        txtUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            txtUploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleTxtFileSelect(files[0]);
            }
        });

        // 选择保存目录
        document.getElementById('selectDirectoryBtn').addEventListener('click', () => {
            this.selectDirectory();
        });

        // 日志面板
        document.getElementById('logToggle').addEventListener('click', () => {
            this.toggleLogPanel();
        });

        document.getElementById('clearLogsBtn').addEventListener('click', () => {
            this.clearLogs();
        });

        document.getElementById('closeLogsBtn').addEventListener('click', () => {
            this.toggleLogPanel();
        });

        // 图片预览控制
        document.getElementById('zoomIn').addEventListener('click', () => {
            this.zoomImage(1.2);
        });

        document.getElementById('zoomOut').addEventListener('click', () => {
            this.zoomImage(0.8);
        });

        document.getElementById('resetZoom').addEventListener('click', () => {
            this.resetImageZoom();
        });

        document.getElementById('downloadImage').addEventListener('click', () => {
            this.downloadCurrentImage();
        });

        // 模态框
        document.getElementById('modalClose').addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('modalCancel').addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('imageModalClose').addEventListener('click', () => {
            this.closeImageModal();
        });

        // 图片导航
        document.getElementById('prevImageBtn').addEventListener('click', () => {
            this.showPreviousImage();
        });

        document.getElementById('nextImageBtn').addEventListener('click', () => {
            this.showNextImage();
        });

        // 键盘导航
        document.addEventListener('keydown', (e) => {
            if (document.getElementById('imageModal').classList.contains('show')) {
                if (e.key === 'ArrowLeft') {
                    e.preventDefault();
                    this.showPreviousImage();
                } else if (e.key === 'ArrowRight') {
                    e.preventDefault();
                    this.showNextImage();
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    this.closeImageModal();
                }
            }
        });

        // 配置按钮
        document.getElementById('configBtn').addEventListener('click', () => {
            this.showConfigModal();
        });

        document.getElementById('addServiceBtn').addEventListener('click', () => {
            this.showAddServiceModal();
        });

        document.getElementById('addCharacterBtn').addEventListener('click', () => {
            this.showAddCharacterModal();
        });

        // Excel编辑器事件 - 添加元素存在性检查
        const excelEditorUploadArea = document.getElementById('excelEditorUploadArea');
        const excelEditorFile = document.getElementById('excelEditorFile');
        const exportEditedExcelBtn = document.getElementById('exportEditedExcelBtn');
        const clearExcelEditorBtn = document.getElementById('clearExcelEditorBtn');

        if (excelEditorUploadArea) {
            excelEditorUploadArea.addEventListener('click', () => {
                if (excelEditorFile) {
                    excelEditorFile.click();
                }
            });

            // Excel编辑器拖拽上传
            excelEditorUploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                excelEditorUploadArea.classList.add('dragover');
            });

            excelEditorUploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                excelEditorUploadArea.classList.remove('dragover');
            });

            excelEditorUploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                excelEditorUploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.handleExcelEditorUpload(files[0]);
                }
            });
        }

        if (excelEditorFile) {
            excelEditorFile.addEventListener('change', (e) => {
                this.handleExcelEditorUpload(e.target.files[0]);
            });
        }

        // Excel编辑器按钮事件
        if (exportEditedExcelBtn) {
            exportEditedExcelBtn.addEventListener('click', () => {
                this.exportEditedExcel();
            });
        }

        if (clearExcelEditorBtn) {
            clearExcelEditorBtn.addEventListener('click', () => {
                this.clearExcelEditor();
            });
        }
    }

    initTabs() {
        this.switchTab('story');
    }

    switchTab(tabName) {
        // 检查Excel编辑器是否有未保存的更改
        const currentTab = document.querySelector('.nav-tab.active');
        if (currentTab && currentTab.dataset.tab === 'excel-editor' && tabName !== 'excel-editor') {
            if (!this.checkUnsavedExcelChanges()) {
                return; // 用户取消切换
            }
        }

        // 更新标签页
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        const targetTab = document.querySelector(`[data-tab="${tabName}"]`);
        if (targetTab) {
            targetTab.classList.add('active');
        }

        // 更新内容
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        const targetContent = document.getElementById(`${tabName}-tab`);
        if (targetContent) {
            targetContent.classList.add('active');
        }

        // 加载对应内容
        if (tabName === 'models') {
            this.loadModelServices();
        } else if (tabName === 'characters') {
            this.loadCharacters();
        } else if (tabName === 'imagehost') {
            this.loadImageHostConfig();
            this.loadUploadHistory();
        } else if (tabName === 'character-gen') {
            this.loadCharacterGenConfig();
        } else if (tabName === 'excel') {
            this.loadExcelConfig();
        }
    }

    async loadConfigs() {
        try {
            // 加载模型服务配置
            const modelConfig = await this.apiCall('/api/config/models', 'GET');
            this.populateModelServices(modelConfig.services);

            // 设置默认选择
            if (modelConfig.default_service) {
                document.getElementById('modelService').value = modelConfig.default_service;
                this.loadModelsForService(modelConfig.default_service);
            }

            // 加载角色配置
            const characters = await this.apiCall('/api/config/characters', 'GET');
            this.characters = characters;

            // 加载保存设置
            await this.loadSaveSettings();

        } catch (error) {
            this.showError('加载配置失败: ' + error.message);
        }
    }

    populateModelServices(services) {
        const select = document.getElementById('modelService');
        select.innerHTML = '<option value="">请选择...</option>';
        
        services.forEach(service => {
            const option = document.createElement('option');
            option.value = service.id;
            option.textContent = service.name;
            select.appendChild(option);
        });
    }

    async loadModelsForService(serviceId) {
        if (!serviceId) {
            document.getElementById('imageModel').innerHTML = '<option value="">请选择...</option>';
            return;
        }

        try {
            const modelConfig = await this.apiCall('/api/config/models', 'GET');
            const service = modelConfig.services.find(s => s.id.toString() === serviceId);

            if (service && service.models) {
                const imageModels = service.models.filter(m => m.type === 'image');
                const select = document.getElementById('imageModel');
                select.innerHTML = '<option value="">请选择...</option>';

                imageModels.forEach((model, index) => {
                    const option = document.createElement('option');
                    option.value = model.name;
                    option.textContent = model.name;
                    select.appendChild(option);
                });

                // 自动选择第一个生图模型
                if (imageModels.length > 0) {
                    select.value = imageModels[0].name;
                    this.addLog(`已自动选择生图模型: ${imageModels[0].name}`);
                }
            }
        } catch (error) {
            this.showError('加载模型失败: ' + error.message);
        }
    }

    async handleExcelUpload(file) {
        if (!file) return;

        const formData = new FormData();
        formData.append('file', file);

        try {
            this.showLoading('正在处理Excel文件...');
            
            const result = await this.apiCall('/api/excel/upload', 'POST', formData);
            
            this.currentExcelData = result;
            this.currentScenes = result.scenes;

            // 为每个分镜设置默认的核心特征开关状态
            this.currentScenes.forEach(scene => {
                if (scene.use_core_features === undefined) {
                    scene.use_core_features = true; // 默认启用
                }
            });

            document.getElementById('selectedFileName').textContent = result.filename;
            document.getElementById('generateAllBtn').disabled = false;
            document.getElementById('saveCacheBtn').disabled = false; // 启用保存缓存按钮
            document.getElementById('clearDataBtn').disabled = false; // 启用清空按钮

            this.renderScenes();
            this.hideLoading();

            this.addLog(`Excel文件处理成功: ${result.filename}, ${result.scenes.length} 个分镜`);

            // 自动保存数据
            this.autoSaveData();

        } catch (error) {
            this.hideLoading();
            this.showError('Excel文件处理失败: ' + error.message);
        }
    }

    renderScenes() {
        const container = document.getElementById('scenesContainer');
        container.innerHTML = '';

        this.currentScenes.forEach((scene, index) => {
            const sceneElement = this.createSceneElement(scene, index);
            container.appendChild(sceneElement);
        });
    }

    createSceneElement(scene, index) {
        const div = document.createElement('div');
        div.className = 'scene-card';
        div.innerHTML = `
            <div class="scene-header">
                <h3 class="scene-title">分镜${scene.scene_number}</h3>
                <input type="checkbox" class="scene-checkbox" ${scene.enabled ? 'checked' : ''} 
                       onchange="app.toggleScene(${index}, this.checked)">
            </div>
            <div class="story-scene-content">
                <div class="scene-prompt">
                    <textarea placeholder="请输入文生图prompt..."
                              onchange="app.updateScenePrompt(${index}, this.value)">${scene.prompt}</textarea>
                    <div class="reference-text-container">
                        <textarea placeholder="参考图说明(自动生成，可手动编辑)"
                                  class="reference-text"
                                  onchange="app.updateReferenceText(${index}, this.value)">${scene.reference_text || ''}</textarea>
                    </div>
                </div>
                <div class="scene-right-panel">
                    <div class="scene-characters">
                        <div class="character-selection" onclick="app.showCharacterSelection(${index})">
                            <i class="fas fa-users"></i><br>
                            选择参考角色<br>
                            <small>(最多5张)</small>
                        </div>
                        <div class="selected-characters" id="selectedChars${index}">
                            ${this.renderSelectedCharacters(scene.selected_characters, index)}
                        </div>
                    </div>
                    <div class="scene-actions">
                        <div class="input-group">
                            <label>生图数量:</label>
                            <input type="number" class="image-count-input" value="${scene.image_count}"
                                   min="1" max="10" onchange="app.updateImageCount(${index}, this.value)">
                        </div>
                        <div class="scene-core-features">
                            <label class="switch">
                                <input type="checkbox" id="coreFeatures_${index}" ${scene.use_core_features !== false ? 'checked' : ''}
                                       onchange="app.updateSceneCoreFeatures(${index}, this.checked)">
                                <span class="slider"></span>
                            </label>
                            <label for="coreFeatures_${index}">核心特征</label>
                        </div>
                        <button class="btn btn-primary" id="generateBtn_${index}" onclick="app.generateSingleScene(${index})">
                            <i class="fas fa-magic" id="generateIcon_${index}"></i>
                            <span id="generateText_${index}">生成图片</span>
                        </button>
                    </div>
                </div>

                <!-- 单个分镜进度显示 -->
                <div class="single-scene-progress" id="sceneProgress_${index}" style="display: none;">
                    <div class="progress-header">
                        <span class="progress-title">分镜${scene.scene_number}生成进度</span>
                        <span class="progress-time" id="sceneProgressTime_${index}">准备中...</span>
                    </div>
                    <div class="progress-details" id="sceneProgressDetails_${index}">
                        <!-- 动态生成的进度条 -->
                    </div>
                </div>
            </div>
            <div class="generated-images">
                <h4>生成的图片:</h4>
                <div class="images-grid force-horizontal-grid super-force-grid" id="images${index}"
                     style="display: grid !important; grid-template-columns: repeat(10, 100px) !important; gap: 10px !important; width: 100% !important; justify-content: start !important;">
                    ${this.renderGeneratedImages(scene.generated_images, index)}
                </div>
            </div>
        `;
        return div;
    }

    renderSelectedCharacters(selectedCharacters, sceneIndex) {
        if (!selectedCharacters || selectedCharacters.length === 0) {
            return '<small>未选择角色</small>';
        }

        return selectedCharacters.map(selectionId => {
            let character = null;
            let outfitName = '';

            // 检查是否为新格式（包含冒号）
            if (selectionId.includes(':')) {
                // 新格式：characterCode:outfitId
                const [characterCode, outfitId] = selectionId.split(':');
                character = this.characters.find(c => c.character_code === characterCode);
                if (character) {
                    const outfit = character.outfits?.find(o => o.outfit_id === outfitId);
                    outfitName = outfit ? ` - ${outfit.outfit_name}` : '';
                }
            } else {
                // 旧格式：直接是角色ID
                character = this.characters.find(c => c.id === selectionId);
            }

            if (character) {
                const characterName = character.character_name || character.name || '未命名角色';
                return `<span class="character-tag">
                    ${characterName}${outfitName}
                    <span class="remove" onclick="app.removeCharacterSelection('${selectionId}', ${sceneIndex})">&times;</span>
                </span>`;
            }
            return '';
        }).join('');
    }

    renderGeneratedImages(images, sceneIndex) {
        if (!images || images.length === 0) {
            return '<small>暂无生成的图片</small>';
        }

        return images.map((img, imgIndex) => `
            <img src="${img.url}" class="image-thumb"
                 onclick="app.showImagePreview('${img.url}', ${sceneIndex}, ${imgIndex})"
                 alt="生成的图片"
                 style="width: 100px !important; height: 178px !important; object-fit: cover !important; border-radius: 8px !important; cursor: pointer !important; border: 2px solid #ddd !important; flex-shrink: 0 !important; display: block !important; float: none !important; position: static !important; margin: 0 !important; padding: 0 !important;">
        `).join('');
    }

    applyBatchCount() {
        const count = parseInt(document.getElementById('batchCount').value);
        if (count >= 1 && count <= 10) {
            this.currentScenes.forEach(scene => {
                scene.image_count = count;
            });
            this.renderScenes();
            this.addLog(`批量设置生图数量为 ${count}`);
        }
    }

    toggleScene(index, enabled) {
        this.currentScenes[index].enabled = enabled;
        this.autoSaveData();
    }

    updateScenePrompt(index, prompt) {
        this.currentScenes[index].prompt = prompt;
        this.autoSaveData();
    }

    updateReferenceText(index, referenceText) {
        this.currentScenes[index].reference_text = referenceText;
        this.autoSaveData();
    }

    updateImageCount(index, count) {
        this.currentScenes[index].image_count = parseInt(count);
        this.autoSaveData();
    }

    updateSceneCoreFeatures(index, enabled) {
        this.currentScenes[index].use_core_features = enabled;
        // 重新生成参考图说明
        const scene = this.currentScenes[index];
        this.generateReferenceText(scene, scene.selected_characters || []);
        this.renderScenes();
        this.autoSaveData();
        this.addLog(`分镜${scene.scene_number}的核心特征开关已${enabled ? '启用' : '禁用'}`);
    }

    applyCoreFeaturesToAll() {
        const globalEnabled = document.getElementById('globalCoreFeatures').checked;
        this.currentScenes.forEach((scene, index) => {
            scene.use_core_features = globalEnabled;
            this.generateReferenceText(scene, scene.selected_characters || []);
        });
        this.renderScenes();
        this.autoSaveData();
        this.addLog(`已将核心特征开关${globalEnabled ? '启用' : '禁用'}应用到所有${this.currentScenes.length}个分镜`);
    }

    showCharacterSelection(sceneIndex) {
        this.currentSceneIndex = sceneIndex;
        this.showModal('选择参考角色', this.createCharacterSelectionContent());

        // 显示"应用到所有分镜"按钮
        document.getElementById('modalApplyAll').style.display = 'inline-flex';

        // 绑定角色选择确认事件
        document.getElementById('modalConfirm').onclick = () => {
            this.confirmCharacterSelection();
        };

        // 绑定"应用到所有分镜"事件
        document.getElementById('modalApplyAll').onclick = () => {
            this.applyCharactersToAllScenes();
        };
    }

    createCharacterSelectionContent() {
        if (!this.characters || this.characters.length === 0) {
            return '<p>暂无可用角色，请先在角色配置中添加角色。</p>';
        }

        const selectedCharacters = this.currentScenes[this.currentSceneIndex]?.selected_characters || [];

        let content = `
            <div class="character-outfit-selection">
                <div class="selection-header">
                    <h4>选择角色和服装</h4>
                    <div class="selection-count">已选择: <span id="selectionCount">${selectedCharacters.length}</span>/5</div>
                </div>

                <div class="character-groups-container" style="max-height: 500px; overflow-y: auto;">
        `;

        // 按角色分组显示
        this.characters.forEach(character => {
            const characterCode = character.character_code || '';
            const characterName = character.character_name || '未命名角色';
            const outfits = character.outfits || [];

            content += `
                <div class="character-group-selection" data-character-code="${characterCode}">
                    <div class="character-group-header">
                        <div class="character-info">
                            <span class="character-name">${characterName}(${characterCode})</span>
                        
                        </div>
                        <div class="outfit-count-badge">${outfits.length} 套服装</div>
                    </div>

                    <div class="outfits-selection-grid">
            `;

            if (outfits.length === 0) {
                content += `
                    <div class="no-outfits-message">
                        <i class="fas fa-tshirt"></i>
                        <p>该角色暂无服装配置</p>
                        <small>请先在角色配置中为该角色添加服装</small>
                    </div>
                `;
            } else {
                outfits.forEach(outfit => {
                    const selectionId = `${characterCode}:${outfit.outfit_id}`;
                    const isSelected = selectedCharacters.includes(selectionId);

                    content += `
                        <div class="outfit-selection-card ${isSelected ? 'selected' : ''}"
                             data-selection-id="${selectionId}"
                             onclick="app.toggleCharacterOutfitSelection('${selectionId}')">
                            <div class="outfit-image-preview">
                                ${outfit.image_url ?
                                    `<img src="${outfit.image_url}" alt="${outfit.outfit_name}">` :
                                    `<div class="no-image"><i class="fas fa-tshirt"></i></div>`
                                }
                            </div>
                            <div class="outfit-selection-info">
                                <div class="outfit-name">${outfit.outfit_name}</div>
                                ${outfit.outfit_description ?
                                    `<div class="outfit-description">${outfit.outfit_description}</div>` : ''
                                }
                            </div>
                            ${isSelected ?
                                '<div class="selection-indicator"><i class="fas fa-check"></i></div>' : ''
                            }
                        </div>
                    `;
                });
            }

            content += `
                    </div>
                </div>
            `;
        });

        content += `
                </div>

                <div class="selection-footer">
                    <div class="selection-tips">
                        <i class="fas fa-lightbulb"></i>
                        <strong>提示：</strong> 最多可选择5个角色服装组合作为参考图。每个组合包含一个角色和一套服装。
                    </div>
                </div>
            </div>
        `;

        return content;
    }

    initCharacterDragSort() {
        const container = document.getElementById('charactersContainer');
        let draggedElement = null;

        container.addEventListener('dragstart', (e) => {
            if (e.target.classList.contains('character-card')) {
                draggedElement = e.target;
                e.target.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
            }
        });

        container.addEventListener('dragend', (e) => {
            if (e.target.classList.contains('character-card')) {
                e.target.classList.remove('dragging');
                draggedElement = null;
            }
        });

        container.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
            
            const afterElement = this.getDragAfterElement(container, e.clientY);
            if (afterElement == null) {
                container.appendChild(draggedElement);
            } else {
                container.insertBefore(draggedElement, afterElement);
            }
        });

        container.addEventListener('drop', (e) => {
            e.preventDefault();
            this.saveCharacterOrder();
        });
    }

    getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.character-card:not(.dragging)')];
        
        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;
            
            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    async saveCharacterOrder() {
        try {
            const characterCards = document.querySelectorAll('.character-card');
            const orderedCharacters = [];
            
            characterCards.forEach(card => {
                const characterId = card.dataset.characterId;
                const character = this.characters.find(c => c.id === characterId);
                if (character) {
                    orderedCharacters.push(character);
                }
            });
            
            await this.apiCall('/api/config/characters', 'POST', orderedCharacters);
            this.characters = orderedCharacters;
            this.addLog('角色排序已保存');
        } catch (error) {
            this.showError('保存角色排序失败: ' + error.message);
        }
    }

    async setDefaultService(serviceId) {
        try {
            const config = await this.apiCall('/api/config/models', 'GET');
            config.default_service = serviceId;
            
            await this.apiCall('/api/config/models', 'POST', config);
            this.addLog('已设置默认模型服务');
            
            // 更新本地状态
            this.defaultService = serviceId;
            
            // 重新加载配置和服务列表
            this.loadConfigs();
            this.loadModelServices();
        } catch (error) {
            this.showError('设置默认服务失败: ' + error.message);
        }
    }

    toggleCharacterSelection(characterId) {
        // 保留旧方法以兼容性，但现在使用新的选择方式
        console.warn('toggleCharacterSelection is deprecated, use toggleCharacterOutfitSelection instead');
    }

    toggleCharacterOutfitSelection(selectionId) {
        const scene = this.currentScenes[this.currentSceneIndex];
        if (!scene.selected_characters) {
            scene.selected_characters = [];
        }

        const index = scene.selected_characters.indexOf(selectionId);
        if (index > -1) {
            // 取消选择
            scene.selected_characters.splice(index, 1);
        } else {
            // 选择角色服装组合
            if (scene.selected_characters.length >= 5) {
                this.showError('最多只能选择5个角色服装组合');
                return;
            }
            scene.selected_characters.push(selectionId);
        }

        // 更新显示
        this.updateCharacterOutfitSelectionDisplay();
    }

    updateCharacterSelectionDisplay() {
        // 保留旧方法以兼容性
        this.updateCharacterOutfitSelectionDisplay();
    }

    updateCharacterOutfitSelectionDisplay() {
        const scene = this.currentScenes[this.currentSceneIndex];
        const selectedCharacters = scene.selected_characters || [];

        // 更新选择计数
        const countElement = document.getElementById('selectionCount');
        if (countElement) {
            countElement.textContent = selectedCharacters.length;
        }

        // 更新模态框中的显示
        document.querySelectorAll('.outfit-selection-card').forEach(card => {
            const selectionId = card.dataset.selectionId;
            const isSelected = selectedCharacters.includes(selectionId);

            if (isSelected) {
                card.classList.add('selected');
                if (!card.querySelector('.selection-indicator')) {
                    card.innerHTML += '<div class="selection-indicator"><i class="fas fa-check"></i></div>';
                }
            } else {
                card.classList.remove('selected');
                const indicator = card.querySelector('.selection-indicator');
                if (indicator) {
                    indicator.remove();
                }
            }
        });

        // 更新分镜中的显示（通过重新渲染实现）
        // this.updateSceneCharacterDisplay(); // 已通过renderScenes实现
    }

    confirmCharacterSelection() {
        const scene = this.currentScenes[this.currentSceneIndex];
        const selectedCharacters = scene.selected_characters || [];

        // 生成参考图说明
        this.generateReferenceText(scene, selectedCharacters);

        this.closeModal();
        this.renderScenes(); // 重新渲染以更新显示
        this.addLog(`分镜${scene.scene_number}的参考角色已更新`);

        // 自动保存数据
        this.autoSaveData();
    }

    applyCharactersToAllScenes() {
        const currentScene = this.currentScenes[this.currentSceneIndex];
        const selectedCharacters = currentScene.selected_characters || [];

        if (selectedCharacters.length === 0) {
            this.showError('请先选择要应用的参考角色');
            return;
        }

        // 应用到所有分镜
        this.currentScenes.forEach((scene, index) => {
            scene.selected_characters = [...selectedCharacters]; // 复制数组
            this.generateReferenceText(scene, selectedCharacters);
        });

        this.closeModal();
        this.renderScenes(); // 重新渲染以更新显示
        this.addLog(`已将选择的${selectedCharacters.length}个参考角色应用到所有${this.currentScenes.length}个分镜`);

        // 自动保存数据
        this.autoSaveData();
    }

    generateReferenceText(scene, selectedCharacters) {
        if (selectedCharacters.length > 0) {
            let referenceText = '';
            selectedCharacters.forEach((selectionId, index) => {
                let character = null;

                // 检查是否为新格式（包含冒号）
                if (selectionId.includes(':')) {
                    // 新格式：characterCode:outfitId
                    const [characterCode, outfitId] = selectionId.split(':');
                    character = this.characters.find(c => c.character_code === characterCode);
                } else {
                    // 旧格式：直接是角色ID
                    character = this.characters.find(c => c.id === selectionId);
                }

                if (character) {
                    const position = ['第一', '第二', '第三', '第四', '第五'][index];
                    // 根据角色的display_type设置选择使用名称还是编码
                    const displayName = character.display_type === 'code' ?
                        (character.character_code || character.code) :
                        (character.character_name || character.name);
                    let characterText = `${displayName}的角色形象:请严格参考我提供的${position}张图片(角色参考图)来塑造角色。`;

                    // 根据分镜的核心特征开关决定是否添加核心特征
                    const useCoreFeatures = scene.use_core_features !== false; // 默认为true
                    if (useCoreFeatures && character.core_features && character.core_features.trim()) {
                        characterText += `角色不可变的核心特征=【${character.core_features.trim()}】`;
                    }

                    referenceText += characterText;
                }
            });
            scene.reference_text = referenceText;
        } else {
            scene.reference_text = '';
        }
    }

    removeCharacter(characterId) {
        // 保留旧方法以兼容性
        this.removeCharacterSelection(characterId);
    }

    removeCharacterSelection(selectionId, sceneIndex = null) {
        // 如果没有提供sceneIndex，使用当前的currentSceneIndex（向后兼容）
        const targetSceneIndex = sceneIndex !== null ? sceneIndex : this.currentSceneIndex;
        const scene = this.currentScenes[targetSceneIndex];

        if (scene && scene.selected_characters) {
            const index = scene.selected_characters.indexOf(selectionId);
            if (index > -1) {
                scene.selected_characters.splice(index, 1);
                // 重新生成参考图说明
                this.generateReferenceText(scene, scene.selected_characters);
                // 更新显示
                this.renderScenes();
                this.autoSaveData();
            }
        }
    }

    async generateSingleScene(sceneIndex) {
        const scene = this.currentScenes[sceneIndex];
        const serviceId = document.getElementById('modelService').value;
        const model = document.getElementById('imageModel').value;
        const saveDirectory = document.getElementById('saveDirectory').value;

        if (!serviceId || !model) {
            this.showError('请先选择模型服务和生图模型');
            return;
        }

        if (!scene.prompt || scene.prompt.trim() === '') {
            this.showError('请输入文生图prompt');
            return;
        }

        // 获取超时配置
        const timeoutConfig = await this.getTimeoutConfig(serviceId);
        const timeoutSeconds = timeoutConfig || 300; // 默认300秒

        try {
            // 显示进度
            this.showSceneProgress(sceneIndex, scene.image_count, timeoutSeconds);

            // 设置按钮加载状态
            this.setSceneButtonLoading(sceneIndex, true);

            const requestData = {
                ...scene,
                service_id: serviceId,
                model: model,
                save_directory: saveDirectory,
                excel_filename: this.currentExcelData?.filename || 'image'
            };

            this.addLog(`开始生成分镜${scene.scene_number}: 服务=${serviceId}, 模型=${model}`);

            const result = await this.apiCall('/api/generate/single', 'POST', requestData);

            if (result.success) {
                this.currentScenes[sceneIndex].generated_images = result.generated_images;
                this.renderScenes();
                this.addLog(`分镜${scene.scene_number}生成成功，共${result.total_count}张图片`);

                // 更新进度为成功
                this.updateSceneProgressSuccess(sceneIndex, result.total_count);

                // 自动保存数据
                this.autoSaveData();
            } else {
                this.showError(`生成失败: ${result.error || '未知错误'}`);
                this.updateSceneProgressError(sceneIndex, result.error || '未知错误');
            }

        } catch (error) {
            const errorMessage = `生成图片失败: ${error.message}`;
            this.showError(errorMessage);
            this.addLog(`分镜${scene.scene_number}生成失败: ${error.message}`, 'error');
            this.updateSceneProgressError(sceneIndex, error.message);
        } finally {
            // 设置按钮恢复状态
            this.setSceneButtonLoading(sceneIndex, false);

            // 3秒后隐藏进度
            setTimeout(() => {
                this.hideSceneProgress(sceneIndex);
            }, 3000);
        }
    }

    async generateAllScenes() {
        const serviceId = document.getElementById('modelService').value;
        const model = document.getElementById('imageModel').value;
        const saveDirectory = document.getElementById('saveDirectory').value;

        if (!serviceId || !model) {
            this.showError('请先选择模型服务和生图模型');
            return;
        }

        const enabledScenes = this.currentScenes.filter(scene => scene.enabled);
        if (enabledScenes.length === 0) {
            this.showError('没有启用的分镜');
            return;
        }

        // 验证所有启用的分镜是否有prompt
        const invalidScenes = enabledScenes.filter(scene => !scene.prompt || scene.prompt.trim() === '');
        if (invalidScenes.length > 0) {
            this.showError(`以下分镜缺少prompt: ${invalidScenes.map(s => s.scene_number).join(', ')}`);
            return;
        }

        try {
            this.isGenerating = true;

            // 获取超时配置
            const timeoutConfig = await this.getTimeoutConfig(serviceId);
            const timeoutSeconds = timeoutConfig || 300;

            // 显示详细的批量进度
            this.showBatchProgress(enabledScenes, timeoutSeconds);

            this.addLog(`开始批量生成: ${enabledScenes.length}个分镜, 服务=${serviceId}, 模型=${model}`);

            const requestData = {
                scenes: enabledScenes.map(scene => ({
                    ...scene,
                    service_id: serviceId,
                    model: model,
                    save_directory: saveDirectory,
                    excel_filename: this.currentExcelData?.filename || 'image'
                }))
            };

            // 开始批量生成，并监控进度
            const result = await this.apiCallWithProgress('/api/generate/batch', 'POST', requestData, enabledScenes);

            if (result.success) {
                // 更新场景数据
                result.results.forEach(sceneResult => {
                    const sceneIndex = this.currentScenes.findIndex(s => s.scene_number === sceneResult.scene_number);
                    if (sceneIndex !== -1) {
                        this.currentScenes[sceneIndex].generated_images = sceneResult.images;
                    }
                });

                this.renderScenes();
                this.addLog(`批量生成完成！总共生成${result.total_images}张图片，成功${result.successful_scenes}个分镜`);

                if (result.failed_scenes.length > 0) {
                    this.addLog(`失败的分镜: ${result.failed_scenes.map(f => `${f.scene_number}(${f.error})`).join(', ')}`, 'error');
                }

                // 更新批量进度为完成
                this.updateBatchProgressComplete(result);
            } else {
                this.showError(`批量生成失败: ${result.error || '未知错误'}`);
                this.updateBatchProgressError(result.error || '未知错误');
            }

        } catch (error) {
            const errorMessage = `批量生成失败: ${error.message}`;
            this.showError(errorMessage);
            this.addLog(errorMessage, 'error');
        } finally {
            this.isGenerating = false;
            // 3秒后隐藏批量进度
            setTimeout(() => {
                this.hideBatchProgress();
            }, 3000);
        }
    }

    // 工具方法
    async apiCall(url, method, data = null) {
        const options = {
            method,
            headers: {}
        };

        if (data) {
            if (data instanceof FormData) {
                options.body = data;
            } else {
                options.headers['Content-Type'] = 'application/json';
                options.body = JSON.stringify(data);
            }
        }

        try {
            const response = await fetch(url, options);
            
            // 检查响应是否为空
            const responseText = await response.text();
            
            if (!responseText) {
                throw new Error('服务器返回空响应');
            }

            // 尝试解析JSON
            let result;
            try {
                result = JSON.parse(responseText);
            } catch (parseError) {
                // 如果JSON解析失败，记录原始响应
                console.error('JSON解析失败:', parseError);
                console.error('原始响应:', responseText);
                throw new Error(`服务器返回无效的JSON响应: ${responseText.substring(0, 100)}...`);
            }

            if (!response.ok) {
                throw new Error(result.error || `HTTP ${response.status}: ${response.statusText}`);
            }

            return result;
        } catch (error) {
            // 改进错误消息
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('网络连接失败，请检查网络连接或服务器状态');
            }
            throw error;
        }
    }

    showModal(title, content) {
        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modalBody').innerHTML = content;
        document.getElementById('modal').classList.add('show');

        // 重置按钮显示
        document.getElementById('modalConfirm').style.display = 'inline-flex';
        document.getElementById('modalCancel').style.display = 'inline-flex';
        document.getElementById('modalApplyAll').style.display = 'none'; // 默认隐藏
    }

    closeModal() {
        document.getElementById('modal').classList.remove('show');
        // 重置按钮显示
        document.getElementById('modalConfirm').style.display = 'inline-flex';
        document.getElementById('modalCancel').style.display = 'inline-flex';
        document.getElementById('modalApplyAll').style.display = 'none'; // 隐藏应用到所有分镜按钮
    }

    showImagePreview(url, sceneIndex = null, imageIndex = null) {
        // 如果没有提供索引，尝试从当前数据中查找
        if (sceneIndex === null || imageIndex === null) {
            const found = this.findImageIndices(url);
            if (found) {
                sceneIndex = found.sceneIndex;
                imageIndex = found.imageIndex;
            }
        }

        this.currentImageUrl = url;
        this.currentSceneIndex = sceneIndex;
        this.currentImageIndex = imageIndex;
        this.currentZoom = 1;

        const img = document.getElementById('previewImage');
        img.src = url;
        img.style.transform = 'scale(1)';
        img.classList.remove('zoomed');

        document.getElementById('zoomLevel').textContent = '100%';
        document.getElementById('imageModal').classList.add('show');

        // 更新导航按钮和信息显示
        this.updateImageNavigation();
    }

    findImageIndices(url) {
        for (let sceneIndex = 0; sceneIndex < this.currentScenes.length; sceneIndex++) {
            const scene = this.currentScenes[sceneIndex];
            if (scene.generated_images) {
                for (let imageIndex = 0; imageIndex < scene.generated_images.length; imageIndex++) {
                    if (scene.generated_images[imageIndex].url === url) {
                        return { sceneIndex, imageIndex };
                    }
                }
            }
        }
        return null;
    }

    updateImageNavigation() {
        // 检查是否在角色生成上下文中
        if (this.currentCharGenContext) {
            const { promptId, imageIndex } = this.currentCharGenContext;
            const prompt = this.charGenPrompts.find(p => p.id === promptId);

            if (prompt && prompt.generated_images) {
                const totalImages = prompt.generated_images.length;
                const currentPosition = imageIndex + 1;

                // 更新信息显示
                document.getElementById('imageInfo').textContent = `${currentPosition} / ${totalImages}`;

                // 更新按钮状态
                const prevBtn = document.getElementById('prevImageBtn');
                const nextBtn = document.getElementById('nextImageBtn');

                prevBtn.disabled = imageIndex <= 0;
                nextBtn.disabled = imageIndex >= totalImages - 1;
                return;
            }
        }

        // 原有的分镜图片导航逻辑
        const { totalImages, currentPosition } = this.getImagePosition();

        // 更新信息显示
        document.getElementById('imageInfo').textContent = `${currentPosition} / ${totalImages}`;

        // 更新按钮状态
        const prevBtn = document.getElementById('prevImageBtn');
        const nextBtn = document.getElementById('nextImageBtn');

        prevBtn.disabled = currentPosition <= 1;
        nextBtn.disabled = currentPosition >= totalImages;
    }

    getImagePosition() {
        let totalImages = 0;
        let currentPosition = 0;

        for (let sceneIndex = 0; sceneIndex < this.currentScenes.length; sceneIndex++) {
            const scene = this.currentScenes[sceneIndex];
            if (scene.generated_images && scene.generated_images.length > 0) {
                for (let imageIndex = 0; imageIndex < scene.generated_images.length; imageIndex++) {
                    totalImages++;
                    if (sceneIndex < this.currentSceneIndex ||
                        (sceneIndex === this.currentSceneIndex && imageIndex < this.currentImageIndex)) {
                        currentPosition++;
                    }
                }
            }
        }

        currentPosition++; // 当前图片位置（1-based）

        return { totalImages, currentPosition };
    }

    closeImageModal() {
        document.getElementById('imageModal').classList.remove('show');
        this.currentImageUrl = null;
        this.currentSceneIndex = null;
        this.currentImageIndex = null;
        this.currentZoom = 1;

        // 清除角色生成上下文
        this.currentCharGenContext = null;
    }

    showPreviousImage() {
        // 检查是否在角色生成上下文中
        if (this.currentCharGenContext) {
            const { promptId, imageIndex } = this.currentCharGenContext;
            const prompt = this.charGenPrompts.find(p => p.id === promptId);

            if (prompt && prompt.generated_images && imageIndex > 0) {
                const newIndex = imageIndex - 1;
                const image = prompt.generated_images[newIndex];
                this.currentCharGenContext.imageIndex = newIndex;
                this.showImagePreview(image.url);
            }
            return;
        }

        // 原有的分镜图片导航逻辑
        const prevImage = this.getPreviousImage();
        if (prevImage) {
            this.showImagePreview(prevImage.url, prevImage.sceneIndex, prevImage.imageIndex);
        }
    }

    showNextImage() {
        // 检查是否在角色生成上下文中
        if (this.currentCharGenContext) {
            const { promptId, imageIndex } = this.currentCharGenContext;
            const prompt = this.charGenPrompts.find(p => p.id === promptId);

            if (prompt && prompt.generated_images && imageIndex < prompt.generated_images.length - 1) {
                const newIndex = imageIndex + 1;
                const image = prompt.generated_images[newIndex];
                this.currentCharGenContext.imageIndex = newIndex;
                this.showImagePreview(image.url);
            }
            return;
        }

        // 原有的分镜图片导航逻辑
        const nextImage = this.getNextImage();
        if (nextImage) {
            this.showImagePreview(nextImage.url, nextImage.sceneIndex, nextImage.imageIndex);
        }
    }

    getPreviousImage() {
        if (this.currentSceneIndex === null || this.currentImageIndex === null) {
            return null;
        }

        // 当前分镜的上一张图片
        if (this.currentImageIndex > 0) {
            const scene = this.currentScenes[this.currentSceneIndex];
            return {
                url: scene.generated_images[this.currentImageIndex - 1].url,
                sceneIndex: this.currentSceneIndex,
                imageIndex: this.currentImageIndex - 1
            };
        }

        // 上一个分镜的最后一张图片
        for (let sceneIndex = this.currentSceneIndex - 1; sceneIndex >= 0; sceneIndex--) {
            const scene = this.currentScenes[sceneIndex];
            if (scene.generated_images && scene.generated_images.length > 0) {
                const lastImageIndex = scene.generated_images.length - 1;
                return {
                    url: scene.generated_images[lastImageIndex].url,
                    sceneIndex: sceneIndex,
                    imageIndex: lastImageIndex
                };
            }
        }

        return null;
    }

    getNextImage() {
        if (this.currentSceneIndex === null || this.currentImageIndex === null) {
            return null;
        }

        const currentScene = this.currentScenes[this.currentSceneIndex];

        // 当前分镜的下一张图片
        if (this.currentImageIndex < currentScene.generated_images.length - 1) {
            return {
                url: currentScene.generated_images[this.currentImageIndex + 1].url,
                sceneIndex: this.currentSceneIndex,
                imageIndex: this.currentImageIndex + 1
            };
        }

        // 下一个分镜的第一张图片
        for (let sceneIndex = this.currentSceneIndex + 1; sceneIndex < this.currentScenes.length; sceneIndex++) {
            const scene = this.currentScenes[sceneIndex];
            if (scene.generated_images && scene.generated_images.length > 0) {
                return {
                    url: scene.generated_images[0].url,
                    sceneIndex: sceneIndex,
                    imageIndex: 0
                };
            }
        }

        return null;
    }

    zoomImage(factor) {
        this.currentZoom *= factor;
        this.currentZoom = Math.max(0.1, Math.min(5, this.currentZoom)); // 限制缩放范围
        
        const img = document.getElementById('previewImage');
        img.style.transform = `scale(${this.currentZoom})`;
        img.classList.toggle('zoomed', this.currentZoom !== 1);
        
        document.getElementById('zoomLevel').textContent = `${Math.round(this.currentZoom * 100)}%`;
    }

    resetImageZoom() {
        this.currentZoom = 1;
        const img = document.getElementById('previewImage');
        img.style.transform = 'scale(1)';
        img.classList.remove('zoomed');
        document.getElementById('zoomLevel').textContent = '100%';
    }

    async downloadCurrentImage() {
        if (!this.currentImageUrl) return;
        
        try {
            const response = await fetch(this.currentImageUrl);
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `image_${Date.now()}.png`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.addLog('图片下载成功');
        } catch (error) {
            this.showError('下载图片失败: ' + error.message);
        }
    }

    showProgress() {
        document.getElementById('progressContainer').style.display = 'block';
    }

    hideProgress() {
        document.getElementById('progressContainer').style.display = 'none';
    }

    updateProgress(current, total, message) {
        const percentage = (current / total) * 100;
        document.getElementById('progressFill').style.width = `${percentage}%`;
        document.getElementById('progressText').textContent = `${message} (${current}/${total})`;
    }

    showLoading(message) {
        // 可以添加加载指示器
        this.addLog(message);
    }

    hideLoading() {
        // 隐藏加载指示器
    }

    toggleLogPanel() {
        document.getElementById('logPanel').classList.toggle('show');
    }

    addLog(message, level = 'info') {
        const logContent = document.getElementById('logContent');
        const entry = document.createElement('div');
        entry.className = `log-entry ${level}`;
        
        const timestamp = new Date().toLocaleString();
        entry.innerHTML = `
            <div class="log-timestamp">${timestamp}</div>
            <div class="log-message">${message}</div>
        `;
        
        logContent.appendChild(entry);
        logContent.scrollTop = logContent.scrollHeight;
    }

    clearLogs() {
        document.getElementById('logContent').innerHTML = '';
    }

    showError(message) {
        this.addLog(message, 'error');
        alert(message); // 临时使用alert，可以改为更好的通知方式
    }

    // 配置管理方法
    async loadModelServices() {
        try {
            const config = await this.apiCall('/api/config/models', 'GET');
            this.modelServices = config.services;
            this.defaultService = config.default_service;
            this.renderModelServices(config.services);
        } catch (error) {
            this.showError('加载模型服务失败: ' + error.message);
        }
    }

    async loadCharacters() {
        try {
            const characters = await this.apiCall('/api/config/characters', 'GET');
            // 确保返回的是数组
            this.characters = Array.isArray(characters) ? characters : [];
            this.renderCharacters(this.characters);
        } catch (error) {
            this.showError('加载角色配置失败: ' + error.message);
            // 在错误情况下也要确保characters是数组
            this.characters = [];
            this.renderCharacters(this.characters);
        }
    }

    renderModelServices(services) {
        const container = document.getElementById('servicesContainer');
        if (!services || services.length === 0) {
            container.innerHTML = '<p class="no-data">暂无模型服务配置</p>';
            return;
        }
        container.innerHTML = services.map(service => this.createServiceConfigHTML(service)).join('');
    }

    renderCharacters(characters) {
        const container = document.getElementById('charactersContainer');
        if (!characters || characters.length === 0) {
            container.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-users"></i>
                    暂无角色配置
                </div>
            `;
            return;
        }

        // 按角色编码分组显示
        container.innerHTML = characters.map(char => this.createCharacterGroupHTML(char)).join('');

        // 初始化拖拽功能
        this.initCharacterDragSort();
    }

    createCharacterGroupHTML(character) {
        const displayTypeText = character.display_type === 'code' ? '角色编码' : '角色名称';
        const characterCode = character.character_code || 'unknown';
        const characterName = character.character_name || '未命名角色';
        const outfits = character.outfits || [];

        return `
            <div class="character-group config-item" data-character-code="${characterCode}">
                <div class="config-item-header">
                    <span class="config-item-title">
                        ${characterName}
                        <span style="opacity: 0.8; font-weight: 350;">(${characterCode})</span>
                    </span>
                    <div class="config-item-actions">
                        <button class="btn btn-sm btn-success" onclick="app.addOutfit('${characterCode}')" title="添加服装">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="btn btn-sm" onclick="app.editCharacter('${characterCode}')" title="编辑角色">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="app.deleteCharacter('${characterCode}')" title="删除角色">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <div class="character-details">
                    <div class="outfits-section">
                        <div class="outfits-header">
                            <h4>服装配置</h4>
                        </div>
                        <div class="outfits-grid">
                            ${outfits.length > 0 ?
                                outfits.map(outfit => this.createOutfitCardHTML(characterCode, outfit)).join('') :
                                `<div class="no-outfits">
                                    <i class="fas fa-tshirt"></i>
                                    <p>暂无服装配置</p>
                                    <button class="btn btn-primary btn-sm" onclick="app.addOutfit('${characterCode}')">
                                        <i class="fas fa-plus"></i> 添加第一套服装
                                    </button>
                                </div>`
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    createOutfitCardHTML(characterCode, outfit) {
        const outfitId = outfit.outfit_id || 'unknown';
        const outfitName = outfit.outfit_name || '未命名服装';
        const imageUrl = outfit.image_url || '';

        return `
            <div class="outfit-card" data-outfit-id="${outfitId}">
                <div class="outfit-image" onclick="app.showSimpleImagePreview('${imageUrl}')" title="点击查看大图">
                    ${imageUrl ?
                        `<img src="${imageUrl}" alt="${outfitName}">` :
                        `<div class="no-image-placeholder">
                            <i class="fas fa-tshirt"></i>
                            <div>无图片</div>
                        </div>`
                    }
                </div>
                <div class="outfit-info">
                    <div class="outfit-name">${outfitName}</div>
                    ${outfit.outfit_description ?
                        `<div class="outfit-description">${outfit.outfit_description}</div>` : ''
                    }
                </div>
                <div class="outfit-actions">
                    <button class="btn btn-xs" onclick="app.editOutfit('${characterCode}', '${outfitId}')" title="编辑服装">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-xs btn-danger" onclick="app.deleteOutfit('${characterCode}', '${outfitId}')" title="删除服装">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    createServiceConfigHTML(service) {
        const isDefault = this.defaultService && this.defaultService.toString() === service.id.toString();
        const starClass = isDefault ? 'fas fa-star' : 'far fa-star';
        const starColor = isDefault ? 'color: #ffc107;' : 'color: #666;';
        const defaultText = isDefault ? ' (默认)' : '';
        
        return `
            <div class="config-item" data-service-id="${service.id}">
                <div class="config-item-header">
                    <span class="config-item-title">${service.name}${defaultText}</span>
                    <div class="config-item-actions">
                        <button class="btn btn-sm" onclick="app.editService('${service.id}')">编辑</button>
                        <button class="btn btn-sm" onclick="app.deleteService('${service.id}')">删除</button>
                        <button class="btn btn-sm" onclick="app.setDefaultService('${service.id}')" title="${isDefault ? '已是默认服务' : '设为默认'}" style="${starColor}">
                            <i class="${starClass}"></i>
                        </button>
                    </div>
                </div>
                <div class="config-details">
                    <p><strong>API地址:</strong> ${service.api_url || '未设置'}</p>
                    <p><strong>模型数量:</strong> ${service.models ? service.models.length : 0}</p>
                    <p><strong>超时时间:</strong> ${service.timeout || 180}秒</p>
                    <p><strong>重试次数:</strong> ${service.retry_count || 3}次</p>
                </div>
            </div>
        `;
    }

    showConfigModal() {
        const configContent = `
            <div class="config-options">
                <h4>配置管理</h4>
                <div class="config-buttons" style="display: flex; flex-direction: column; gap: 15px; margin-top: 20px;">
                    <button class="btn btn-primary" onclick="app.exportConfig()">
                        <i class="fas fa-download"></i> 导出配置
                    </button>
                    <button class="btn btn-secondary" onclick="app.importConfig()">
                        <i class="fas fa-upload"></i> 导入配置
                    </button>
                    <button class="btn btn-outline" onclick="app.resetConfig()">
                        <i class="fas fa-refresh"></i> 重置配置
                    </button>
                </div>
                <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <small><strong>说明：</strong><br>
                    • 导出：将当前所有配置保存为JSON文件<br>
                    • 导入：从JSON文件恢复配置<br>
                    • 重置：清除所有配置，恢复默认设置</small>
                </div>
            </div>
        `;
        
        this.showModal('系统配置', configContent);
        
        // 隐藏默认的确认和取消按钮，因为我们有自定义按钮
        document.getElementById('modalConfirm').style.display = 'none';
        document.getElementById('modalCancel').style.display = 'none';
    }

    async exportConfig() {
        try {
            const config = await this.apiCall('/api/config/export', 'GET');
            
            const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `config_${new Date().toISOString().slice(0, 10)}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.addLog('配置导出成功');
            this.closeModal();
        } catch (error) {
            this.showError('导出配置失败: ' + error.message);
        }
    }

    importConfig() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            try {
                const text = await file.text();
                const config = JSON.parse(text);
                
                await this.apiCall('/api/config/import', 'POST', config);
                
                this.addLog('配置导入成功');
                this.closeModal();
                this.loadConfigs(); // 重新加载配置
                this.loadModelServices();
                this.loadCharacters();
            } catch (error) {
                this.showError('导入配置失败: ' + error.message);
            }
        };
        input.click();
    }

    async resetConfig() {
        if (!confirm('确定要重置所有配置吗？此操作不可恢复！')) {
            return;
        }
        
        try {
            const defaultConfig = {
                model_services: [],
                characters: [],
                settings: {
                    save_directory: "D:\\BaiduSyncdisk\\Youtube\\00待剪辑",
                    timeout: 180,
                    retry_count: 3
                }
            };
            
            await this.apiCall('/api/config/import', 'POST', defaultConfig);
            
            this.addLog('配置已重置');
            this.closeModal();
            this.loadConfigs();
            this.loadModelServices();
            this.loadCharacters();
        } catch (error) {
            this.showError('重置配置失败: ' + error.message);
        }
    }

    showAddServiceModal() {
        this.currentEditingService = null;
        this.showModal('添加模型服务', this.createServiceFormHTML());
        this.bindModalEvents('service');
    }

    showAddCharacterModal() {
        this.currentEditingCharacter = null;
        this.showModal('添加角色', this.createCharacterFormHTML());
        this.bindModalEvents('character');
        this.bindCharacterCodeValidation();
    }

    editService(serviceId) {
        const service = this.modelServices?.find(s => s.id.toString() === serviceId.toString());
        if (!service) {
            this.showError('服务不存在');
            return;
        }
        
        this.currentEditingService = service;
        this.showModal('编辑模型服务', this.createServiceFormHTML(service));
        this.bindModalEvents('service');
    }

    editCharacter(characterCode) {
        const character = this.characters?.find(c => c.character_code === characterCode);
        if (!character) {
            this.showError('角色不存在');
            return;
        }

        this.currentEditingCharacter = character;
        this.showModal('编辑角色', this.createCharacterFormHTML(character));
        this.bindModalEvents('character');
        this.bindCharacterCodeValidation();
    }

    async deleteService(serviceId) {
        if (!confirm('确定要删除这个模型服务吗？')) {
            return;
        }

        try {
            const config = await this.apiCall('/api/config/models', 'GET');
            const updatedServices = config.services.filter(s => s.id.toString() !== serviceId.toString());
            
            await this.apiCall('/api/config/models', 'POST', {
                services: updatedServices,
                default_service: config.default_service,
                default_model: config.default_model,
                settings: config.settings
            });

            this.addLog(`删除模型服务成功`);
            this.loadModelServices();
            this.loadConfigs(); // 重新加载配置
        } catch (error) {
            this.showError('删除模型服务失败: ' + error.message);
        }
    }

    async deleteCharacter(characterCode) {
        const character = this.characters?.find(c => c.character_code === characterCode);
        if (!character) {
            this.showError('角色不存在');
            return;
        }

        const outfitCount = character.outfits ? character.outfits.length : 0;
        const confirmMessage = outfitCount > 0 ?
            `确定要删除角色"${character.character_name}"吗？这将同时删除该角色的${outfitCount}套服装。` :
            `确定要删除角色"${character.character_name}"吗？`;

        if (!confirm(confirmMessage)) {
            return;
        }

        try {
            // 从数组中移除角色
            const updatedCharacters = this.characters.filter(char => char.character_code !== characterCode);

            await this.apiCall('/api/config/characters', 'POST', updatedCharacters);

            this.addLog(`删除角色"${character.character_name}"成功`);
            this.loadCharacters();
        } catch (error) {
            this.showError('删除角色失败: ' + error.message);
        }
    }

    bindModalEvents(type) {
        const confirmBtn = document.getElementById('modalConfirm');
        const cancelBtn = document.getElementById('modalCancel');
        
        // 移除之前的事件监听器
        confirmBtn.onclick = null;
        cancelBtn.onclick = null;
        
        confirmBtn.onclick = () => {
            if (type === 'service') {
                this.saveService();
            } else if (type === 'character') {
                this.saveCharacter();
            } else if (type === 'outfit') {
                this.saveOutfit();
            }
        };
        
        cancelBtn.onclick = () => {
            this.closeModal();
        };
    }

    async saveService() {
        const formData = this.getServiceFormData();
        if (!formData) return;

        try {
            const config = await this.apiCall('/api/config/models', 'GET');
            let services = config.services || [];

            if (this.currentEditingService) {
                // 编辑现有服务
                const index = services.findIndex(s => s.id.toString() === this.currentEditingService.id.toString());
                if (index !== -1) {
                    services[index] = { ...services[index], ...formData };
                }
            } else {
                // 添加新服务
                // 检查名称是否重复
                if (services.some(s => s.name === formData.name)) {
                    this.showError('服务名称已存在，请使用不同的名称');
                    return;
                }
                
                const newId = services.length > 0 ? Math.max(...services.map(s => s.id)) + 1 : 1;
                formData.id = newId;
                services.push(formData);
            }

            await this.apiCall('/api/config/models', 'POST', {
                services: services,
                default_service: config.default_service,
                default_model: config.default_model,
                settings: config.settings
            });

            this.addLog(`${this.currentEditingService ? '编辑' : '添加'}模型服务成功`);
            this.closeModal();
            this.loadModelServices();
            this.loadConfigs(); // 重新加载配置
        } catch (error) {
            this.showError(`${this.currentEditingService ? '编辑' : '添加'}模型服务失败: ` + error.message);
        }
    }

    async saveCharacter() {
        const formData = this.getCharacterFormData();
        if (!formData) return;

        try {
            let characters = [...(this.characters || [])];
            const originalCode = this.currentEditingCharacter?.character_code || '';

            // 检查编码是否重复（排除自身）
            const isDuplicate = characters.some(c =>
                c.character_code === formData.character_code &&
                c.character_code !== originalCode
            );

            if (isDuplicate) {
                this.showError('角色编码已存在，请使用不同的编码');

                // 高亮显示错误的输入框
                const codeInput = document.getElementById('characterCode');
                const validationMessage = document.getElementById('codeValidationMessage');
                if (codeInput && validationMessage) {
                    this.showCodeValidation('error', '角色编码已存在，请使用其他编码', codeInput, validationMessage);
                }
                return;
            }

            if (this.currentEditingCharacter) {
                // 编辑现有角色
                const index = characters.findIndex(c => c.character_code === originalCode);
                if (index !== -1) {
                    // 保留原有的服装数据和时间戳
                    characters[index] = {
                        ...characters[index],
                        ...formData,
                        updated_at: new Date().toISOString()
                    };
                }
            } else {
                // 添加新角色
                const newCharacter = {
                    character_code: formData.character_code,
                    character_name: formData.character_name,
                    core_features: formData.core_features,
                    display_type: formData.display_type,
                    outfits: [],
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                characters.push(newCharacter);
            }

            await this.apiCall('/api/config/characters', 'POST', characters);

            this.addLog(`${this.currentEditingCharacter ? '编辑' : '添加'}角色成功`);
            this.closeModal();
            this.loadCharacters();
        } catch (error) {
            this.showError(`${this.currentEditingCharacter ? '编辑' : '添加'}角色失败: ` + error.message);
        }
    }

    getServiceFormData() {
        const name = document.getElementById('serviceName')?.value?.trim();
        const apiUrl = document.getElementById('serviceUrl')?.value?.trim();
        const apiKey = document.getElementById('serviceKey')?.value?.trim();
        const timeout = parseInt(document.getElementById('serviceTimeout')?.value) || 180;
        const retryCount = parseInt(document.getElementById('serviceRetry')?.value) || 3;
        const models = this.getCurrentModelsFromForm();

        if (!name) {
            this.showError('请输入服务名称');
            return null;
        }

        if (!apiUrl) {
            this.showError('请输入API地址');
            return null;
        }

        if (!apiKey) {
            this.showError('请输入API密钥');
            return null;
        }

        return {
            name,
            api_url: apiUrl,
            api_key: apiKey,
            timeout,
            retry_count: retryCount,
            models: models
        };
    }

    getCharacterFormData() {
        const name = document.getElementById('characterName')?.value?.trim();
        const code = document.getElementById('characterCode')?.value?.trim();
        const coreFeatures = document.getElementById('characterCoreFeatures')?.value?.trim();
        const displayType = document.getElementById('characterDisplayType')?.value || 'name';

        if (!name) {
            this.showError('请输入角色名称');
            return null;
        }

        if (!code) {
            this.showError('请输入角色编码');
            return null;
        }

        return {
            character_name: name,
            character_code: code,
            core_features: coreFeatures || '',
            display_type: displayType
        };
    }

    createServiceFormHTML(service = null) {
        const models = service?.models || [];
        
        return `
            <div class="config-form" style="display: block;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div class="input-group">
                        <label>服务名称 <span style="color: red;">*</span>:</label>
                        <input type="text" id="serviceName" class="text-input" value="${service?.name || ''}" required>
                    </div>
                    <div class="input-group">
                        <label>API地址 <span style="color: red;">*</span>:</label>
                        <input type="url" id="serviceUrl" class="text-input" value="${service?.api_url || ''}" placeholder="https://api.example.com/v1/chat/completions" required>
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div class="input-group">
                        <label>API密钥 <span style="color: red;">*</span>:</label>
                        <input type="password" id="serviceKey" class="text-input" value="${service?.api_key || ''}" required>
                    </div>
                    <div class="input-group">
                        <label>超时时间(秒):</label>
                        <input type="number" id="serviceTimeout" class="number-input" value="${service?.timeout || 180}" min="30" max="600">
                    </div>
                    <div class="input-group">
                        <label>重试次数:</label>
                        <input type="number" id="serviceRetry" class="number-input" value="${service?.retry_count || 3}" min="1" max="10">
                    </div>
                </div>
                
                <div class="models-section" style="border-top: 1px solid #e9ecef; padding-top: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="margin: 0; color: #2c3e50;">模型配置</h4>
                        <button type="button" class="btn btn-sm btn-primary" onclick="app.addModelToService()">
                            <i class="fas fa-plus"></i> 添加模型
                        </button>
                    </div>
                    <div id="modelsList" class="models-list">
                        ${this.renderModelsList(models)}
                    </div>
                </div>
                
                <div class="form-note" style="margin-top: 15px;">
                    <small><span style="color: red;">*</span> 为必填项<br>
                    模型类型：chat(对话模型) 或 image(生图模型)</small>
                </div>
            </div>
        `;
    }

    renderModelsList(models) {
        if (!models || models.length === 0) {
            return '<p class="no-models" style="color: #666; font-style: italic; text-align: center; padding: 20px; margin: 0;">暂无模型配置，点击上方按钮添加</p>';
        }

        return models.map((model, index) => `
            <div class="model-item" data-index="${index}">
                <input type="text" placeholder="模型名称" value="${model.name || ''}" class="text-input model-name" style="flex: 1;">
                <select class="select-input model-type" style="width: 140px;">
                    <option value="chat" ${model.type === 'chat' ? 'selected' : ''}>对话模型</option>
                    <option value="image" ${model.type === 'image' ? 'selected' : ''}>生图模型</option>
                </select>
                <button type="button" class="btn btn-sm" onclick="app.removeModelFromService(${index})" 
                        style="color: #dc3545; border-color: #dc3545;" 
                        title="删除模型">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }

    addModelToService() {
        const modelsList = document.getElementById('modelsList');
        const currentModels = this.getCurrentModelsFromForm();
        
        currentModels.push({ name: '', type: 'image' });
        modelsList.innerHTML = this.renderModelsList(currentModels);
    }

    removeModelFromService(index) {
        const modelsList = document.getElementById('modelsList');
        const currentModels = this.getCurrentModelsFromForm();
        
        currentModels.splice(index, 1);
        modelsList.innerHTML = this.renderModelsList(currentModels);
    }

    getCurrentModelsFromForm() {
        const modelItems = document.querySelectorAll('.model-item');
        const models = [];
        
        modelItems.forEach(item => {
            const name = item.querySelector('.model-name').value.trim();
            const type = item.querySelector('.model-type').value;
            
            if (name) { // 只保存有名称的模型
                models.push({ name, type });
            }
        });
        
        return models;
    }

    createCharacterFormHTML(character = null) {
        const isEdit = character !== null;

        return `
            <form id="characterForm" class="modern-form">
                <div class="form-header">
                    <h3>${isEdit ? '编辑角色' : '添加新角色'}</h3>
                    <p class="form-subtitle">配置角色的基本信息和显示设置</p>
                </div>

                <div class="form-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="characterName" class="form-label">
                                <i class="fas fa-signature"></i>
                                角色名称 <span class="required">*</span>
                            </label>
                            <input type="text" id="characterName" class="form-input"
                                   value="${character?.character_name || ''}"
                                   placeholder="例如：小明、艾莉丝等" required>
                        </div>

                        <div class="form-group">
                            <label for="characterCode" class="form-label">
                                <i class="fas fa-code"></i>
                                角色编码 <span class="required">*</span>
                            </label>
                            <input type="text" id="characterCode" class="form-input"
                                   value="${character?.character_code || ''}"
                                   placeholder="输入唯一的角色编码"
                                   data-original-code="${character?.character_code || ''}"
                                   required>
                            <div class="form-help">
                                <i class="fas fa-info-circle"></i>
                                ${isEdit ? '角色编码可以修改，但不能与其他角色重复' : '角色编码必须唯一，长度2-50个字符'}
                            </div>
                            <div id="codeValidationMessage" class="code-validation-message" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="characterCoreFeatures" class="form-label">
                            <i class="fas fa-star"></i>
                            角色核心特征
                        </label>
                        <textarea id="characterCoreFeatures" class="form-textarea" rows="4"
                                  placeholder="描述角色不可变的核心特征，如外观、特殊标识、性格特点等...">${character?.core_features || ''}</textarea>
                        <div class="form-help">
                            <i class="fas fa-info-circle"></i>
                            可选项。如果填写，将在参考图说明中添加角色核心特征描述
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="characterDisplayType" class="form-label">
                            <i class="fas fa-eye"></i>
                            参考图说明中使用
                        </label>
                        <select id="characterDisplayType" class="form-select">
                            <option value="name" ${(!character?.display_type || character?.display_type === 'name') ? 'selected' : ''}>角色名称</option>
                            <option value="code" ${character?.display_type === 'code' ? 'selected' : ''}>角色编码</option>
                        </select>
                        <div class="form-help">
                            <i class="fas fa-info-circle"></i>
                            选择在生成参考图说明时使用角色名称还是角色编码
                        </div>
                    </div>
                </div>
            </form>
        `;
    }

    async selectDirectory() {
        try {
            const currentDir = document.getElementById('saveDirectory').value;

            this.addLog('正在打开文件夹选择对话框...');

            const response = await this.apiCall('/api/select-directory', 'POST', {
                current_directory: currentDir
            });

            if (response.success && response.directory) {
                document.getElementById('saveDirectory').value = response.directory;
                this.addLog(`保存目录已更新: ${response.directory}`);
            } else if (response.message) {
                this.addLog(response.message);
            } else {
                this.showError('选择目录失败: ' + (response.error || '未知错误'));
            }
        } catch (error) {
            this.showError('选择目录失败: ' + error.message);
            // 如果API调用失败，回退到原来的输入方式
            this.addLog('回退到手动输入模式...');
            const currentDir = document.getElementById('saveDirectory').value;
            const newDir = prompt('请输入保存目录路径：', currentDir);

            if (newDir && newDir.trim() !== '') {
                document.getElementById('saveDirectory').value = newDir.trim();
                this.addLog(`保存目录已更新: ${newDir.trim()}`);
            }
        }
    }

    async exportExcel() {
        if (!this.currentScenes || this.currentScenes.length === 0) {
            this.showError('没有可导出的数据');
            return;
        }

        try {
            this.addLog('正在导出Excel文件...');
            
            const exportData = {
                scenes: this.currentScenes,
                filename: `exported_scenes_${new Date().toISOString().slice(0, 10)}.xlsx`
            };
            
            const response = await fetch('/api/excel/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(exportData)
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
            }
            
            // 下载文件
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = exportData.filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.addLog(`Excel文件导出成功: ${exportData.filename}`);
            
        } catch (error) {
            this.showError('导出Excel失败: ' + error.message);
        }
    }

    // 数据持久化相关方法
    saveDataToStorage() {
        try {
            const dataToSave = {
                currentExcelData: this.currentExcelData,
                currentScenes: this.currentScenes,
                timestamp: new Date().toISOString()
            };

            localStorage.setItem('auto_image_app_data', JSON.stringify(dataToSave));
            this.addLog('数据已自动保存到本地存储');
        } catch (error) {
            console.error('保存数据失败:', error);
        }
    }

    loadSavedData() {
        try {
            const savedData = localStorage.getItem('auto_image_app_data');
            if (savedData) {
                const parsedData = JSON.parse(savedData);

                let hasData = false;

                // 恢复Excel数据
                if (parsedData.currentExcelData) {
                    this.currentExcelData = parsedData.currentExcelData;
                    document.getElementById('selectedFileName').textContent = this.currentExcelData.filename;
                    hasData = true;
                }

                // 恢复分镜数据
                if (parsedData.currentScenes && parsedData.currentScenes.length > 0) {
                    this.currentScenes = parsedData.currentScenes;

                    // 为旧数据设置默认的核心特征开关状态
                    this.currentScenes.forEach(scene => {
                        if (scene.use_core_features === undefined) {
                            scene.use_core_features = true; // 默认启用
                        }
                    });

                    this.renderScenes();

                    // 启用生成按钮
                    document.getElementById('generateAllBtn').disabled = false;
                    hasData = true;
                }

                // 如果有任何数据，启用相关按钮
                if (hasData) {
                    document.getElementById('saveCacheBtn').disabled = false;
                    document.getElementById('clearDataBtn').disabled = false;

                    const saveTime = new Date(parsedData.timestamp).toLocaleString();
                    this.addLog(`已恢复上次保存的数据 (保存时间: ${saveTime})`);
                }
            }
        } catch (error) {
            console.error('加载保存的数据失败:', error);
        }
    }

    clearAllData() {
        if (!confirm('确定要清空所有数据吗？这将清除当前的Excel文件和所有分镜数据，此操作不可恢复！')) {
            return;
        }

        try {
            // 清空内存数据
            this.currentExcelData = null;
            this.currentScenes = [];

            // 清空本地存储
            localStorage.removeItem('auto_image_app_data');

            // 重置UI
            document.getElementById('selectedFileName').textContent = '';
            document.getElementById('scenesContainer').innerHTML = '';
            document.getElementById('generateAllBtn').disabled = true;
            document.getElementById('saveCacheBtn').disabled = true;
            document.getElementById('clearDataBtn').disabled = true;

            this.addLog('所有数据已清空');
        } catch (error) {
            this.showError('清空数据失败: ' + error.message);
        }
    }

    // 手动保存缓存
    manualSaveCache() {
        try {
            if (!this.currentExcelData && this.currentScenes.length === 0) {
                this.showError('没有数据需要保存');
                return;
            }

            // 强制保存数据
            this.saveDataToStorage();

            // 验证保存是否成功
            const savedData = localStorage.getItem('auto_image_app_data');
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                const saveTime = new Date(parsedData.timestamp).toLocaleString();

                // 统计保存的数据
                const excelInfo = parsedData.currentExcelData ?
                    `Excel文件: ${parsedData.currentExcelData.filename}` : '无Excel文件';
                const scenesInfo = parsedData.currentScenes ?
                    `${parsedData.currentScenes.length}个分镜` : '无分镜数据';

                // 统计参考角色和生成图片
                let totalCharacters = 0;
                let totalImages = 0;
                if (parsedData.currentScenes) {
                    parsedData.currentScenes.forEach(scene => {
                        if (scene.selected_characters) {
                            totalCharacters += scene.selected_characters.length;
                        }
                        if (scene.generated_images) {
                            totalImages += scene.generated_images.length;
                        }
                    });
                }

                this.addLog(`✅ 缓存保存成功！`);
                this.addLog(`📁 ${excelInfo}`);
                this.addLog(`🎬 ${scenesInfo}`);
                this.addLog(`👥 ${totalCharacters}个参考角色选择`);
                this.addLog(`🖼️ ${totalImages}张生成图片`);
                this.addLog(`⏰ 保存时间: ${saveTime}`);

                // 显示数据大小
                const dataSize = new Blob([savedData]).size;
                this.addLog(`💾 数据大小: ${(dataSize / 1024).toFixed(2)} KB`);

            } else {
                this.showError('缓存保存失败：无法验证保存结果');
            }

        } catch (error) {
            this.showError('手动保存缓存失败: ' + error.message);
        }
    }

    // 在关键操作后自动保存数据
    autoSaveData() {
        if (this.currentExcelData || this.currentScenes.length > 0) {
            this.saveDataToStorage();
        }
    }

    // 图床功能相关方法
    async loadImageHostConfig() {
        try {
            const config = await this.apiCall('/api/imagehost/config', 'GET');

            document.getElementById('githubToken').value = config.github_token || '';
            document.getElementById('repoPath').value = config.repo_path || '/scys-wcs/photo/contents/image';
            document.getElementById('maxFileSize').value = config.max_file_size || 3;

        } catch (error) {
            this.showError('加载图床配置失败: ' + error.message);
        }
    }

    async saveImageHostConfig() {
        try {
            const config = {
                github_token: document.getElementById('githubToken').value.trim(),
                repo_path: document.getElementById('repoPath').value.trim(),
                max_file_size: parseInt(document.getElementById('maxFileSize').value) || 3
            };

            if (!config.github_token) {
                this.showError('请输入GitHub Token');
                return;
            }

            if (!config.repo_path) {
                this.showError('请输入仓库路径');
                return;
            }

            // 验证仓库路径格式
            if (!config.repo_path.match(/^\/[\w-]+\/[\w-]+\/contents\/[\w\/.-]*$/)) {
                this.showError('仓库路径格式错误，应为: /username/repo/contents/path');
                return;
            }

            const result = await this.apiCall('/api/imagehost/config', 'POST', config);

            if (result.success) {
                this.addLog('图床配置保存成功');

                // 测试连接
                const testResult = await this.apiCall('/api/imagehost/test', 'POST', config);
                if (testResult.success) {
                    this.addLog(`✅ ${testResult.message}`);
                } else {
                    this.addLog(`⚠️ 配置已保存，但连接测试失败: ${testResult.error}`);
                }
            } else {
                this.showError('保存图床配置失败: ' + result.error);
            }

        } catch (error) {
            this.showError('保存图床配置失败: ' + error.message);
        }
    }

    handleFileSelect(file) {
        if (!file) return;

        // 检查文件类型
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            this.showError('不支持的文件格式，请选择 JPG、PNG 或 GIF 格式的图片');
            return;
        }

        // 检查文件大小
        const maxSize = parseInt(document.getElementById('maxFileSize').value) || 3;
        const maxSizeBytes = maxSize * 1024 * 1024;
        if (file.size > maxSizeBytes) {
            this.showError(`文件大小超过限制 (${maxSize}MB)，当前文件大小: ${(file.size / 1024 / 1024).toFixed(2)}MB`);
            return;
        }

        this.uploadImage(file);
    }

    async uploadImage(file) {
        try {
            // 显示上传进度
            document.getElementById('uploadProgress').style.display = 'block';
            document.getElementById('uploadResult').style.display = 'none';

            const progressFill = document.getElementById('uploadProgressFill');
            const progressText = document.getElementById('uploadProgressText');

            progressText.textContent = '准备上传...';
            progressFill.style.width = '10%';

            // 创建FormData
            const formData = new FormData();
            formData.append('file', file);

            progressText.textContent = '正在上传...';
            progressFill.style.width = '50%';

            // 上传文件
            const response = await fetch('/api/imagehost/upload', {
                method: 'POST',
                body: formData
            });

            progressFill.style.width = '90%';

            const result = await response.json();

            if (result.success) {
                progressText.textContent = '上传完成';
                progressFill.style.width = '100%';

                // 显示结果
                setTimeout(() => {
                    document.getElementById('uploadProgress').style.display = 'none';
                    this.showUploadResult(result);
                }, 500);

                this.addLog(`✅ 图片上传成功: ${result.filename}`);
                this.addLog(`🔗 图片链接: ${result.url}`);

                // 刷新历史记录
                this.loadUploadHistory();

            } else {
                throw new Error(result.error || '上传失败');
            }

        } catch (error) {
            document.getElementById('uploadProgress').style.display = 'none';
            this.showError('上传失败: ' + error.message);
        }
    }

    showUploadResult(result) {
        const resultDiv = document.getElementById('uploadResult');
        const uploadedImage = document.getElementById('uploadedImage');
        const imageUrl = document.getElementById('imageUrl');

        uploadedImage.src = result.url;
        imageUrl.value = result.url;

        resultDiv.style.display = 'block';
    }

    copyToClipboard(elementId) {
        const element = document.getElementById(elementId);
        element.select();
        element.setSelectionRange(0, 99999);

        try {
            document.execCommand('copy');
            this.addLog('✅ 链接已复制到剪贴板');
        } catch (err) {
            this.showError('复制失败，请手动复制');
        }
    }

    // 角色形象生成功能相关方法
    async loadCharacterGenConfig() {
        try {
            // 加载模型服务配置
            await this.loadCharGenConfigs();

            // 恢复角色生成数据
            this.restoreCharGenData();

        } catch (error) {
            this.showError('加载角色形象生成配置失败: ' + error.message);
        }
    }

    async loadCharGenConfigs() {
        try {
            const modelConfig = await this.apiCall('/api/config/models', 'GET');

            const serviceSelect = document.getElementById('charGenModelService');
            serviceSelect.innerHTML = '<option value="">请选择...</option>';

            if (modelConfig.services) {
                modelConfig.services.forEach(service => {
                    const option = document.createElement('option');
                    option.value = service.id;
                    option.textContent = service.name;
                    serviceSelect.appendChild(option);
                });

                // 自动选择默认服务
                if (modelConfig.default_service) {
                    serviceSelect.value = modelConfig.default_service;
                    await this.loadCharGenModelsForService(modelConfig.default_service);
                }
            }

            // 设置默认保存目录，与一键生成故事模块保持一致
            const storyModuleSaveDir = document.getElementById('saveDirectory');
            if (storyModuleSaveDir && storyModuleSaveDir.value) {
                // 如果一键生成故事模块已有保存目录，使用相同的目录
                document.getElementById('charGenSaveDir').value = storyModuleSaveDir.value;
            } else {
                // 否则尝试从配置中获取
                try {
                    const config = await this.apiCall('/api/config', 'GET');
                    if (config.settings && config.settings.save_directory) {
                        document.getElementById('charGenSaveDir').value = config.settings.save_directory;
                    }
                } catch (error) {
                    // 如果获取配置失败，使用默认值
                    console.log('使用默认保存目录');
                }
            }

        } catch (error) {
            this.showError('加载角色生成配置失败: ' + error.message);
        }
    }

    async loadCharGenModelsForService(serviceId) {
        if (!serviceId) {
            document.getElementById('charGenImageModel').innerHTML = '<option value="">请选择...</option>';
            return;
        }

        try {
            const modelConfig = await this.apiCall('/api/config/models', 'GET');
            const service = modelConfig.services.find(s => s.id.toString() === serviceId);

            if (service && service.models) {
                const imageModels = service.models.filter(m => m.type === 'image');
                const select = document.getElementById('charGenImageModel');
                select.innerHTML = '<option value="">请选择...</option>';

                imageModels.forEach((model, index) => {
                    const option = document.createElement('option');
                    option.value = model.name;
                    option.textContent = model.name;
                    select.appendChild(option);
                });

                // 自动选择第一个生图模型
                if (imageModels.length > 0) {
                    select.value = imageModels[0].name;
                    this.addLog(`已自动选择生图模型: ${imageModels[0].name}`);
                }
            }
        } catch (error) {
            this.showError('加载模型失败: ' + error.message);
        }
    }

    addCharacterGenPrompt() {
        if (!this.charGenPrompts) {
            this.charGenPrompts = [];
        }

        const promptId = Date.now().toString();
        const globalImageCount = parseInt(document.getElementById('charGenImageCount').value) || 1;

        const prompt = {
            id: promptId,
            prompt: '',
            enabled: true,
            selected_characters: [],
            reference_text: '',
            image_count: globalImageCount,
            generated_images: []
        };

        this.charGenPrompts.push(prompt);
        this.renderCharGenPrompts();
        this.updateCharGenButtons();
        this.saveCharGenData();
    }

    renderCharGenPrompts() {
        const container = document.getElementById('charGenPromptsContainer');
        container.innerHTML = '';

        if (!this.charGenPrompts || this.charGenPrompts.length === 0) {
            container.innerHTML = '<div class="empty-state">点击"添加角色提示词"开始创建角色形象</div>';
            return;
        }

        this.charGenPrompts.forEach((prompt, index) => {
            const promptElement = this.createCharGenPromptElement(prompt, index);
            container.appendChild(promptElement);
        });
    }

    createCharGenPromptElement(prompt, index) {
        const div = document.createElement('div');
        div.className = 'scene-card';
        div.innerHTML = `
            <div class="scene-header">
                <div class="scene-title">
                    <span class="scene-number">角色 ${index + 1}</span>
                    <label class="checkbox-label">
                        <input type="checkbox" ${prompt.enabled ? 'checked' : ''}
                               onchange="app.toggleCharGenPrompt('${prompt.id}')">
                        启用
                    </label>
                </div>
                <button class="btn btn-danger btn-sm" onclick="app.removeCharGenPrompt('${prompt.id}')">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>

            <div class="scene-content">
                <div class="prompt-section">
                    <label>角色提示词:</label>
                    <textarea class="chargen-prompt" placeholder="请输入角色生成提示词..."
                              onchange="app.updateCharGenPrompt('${prompt.id}', this.value)">${prompt.prompt}</textarea>
                </div>

                <div class="character-selection-section">
                    <label>选择参考角色:</label>
                    <div class="character-selection-container" id="charGenCharacters_${prompt.id}">
                        <!-- 角色选择框将在这里生成 -->
                    </div>
                </div>

                <div class="reference-text-container">
                    <label>参考图说明:</label>
                    <textarea class="chargen-reference-text" readonly placeholder="选择参考角色后自动生成...">${prompt.reference_text}</textarea>
                </div>

                <div class="image-section">
                    <div class="image-controls">
                        <button class="btn btn-primary" id="generateBtn_${prompt.id}" onclick="app.generateCharGenImages('${prompt.id}')">
                            <i class="fas fa-image" id="generateIcon_${prompt.id}"></i>
                            <span id="generateText_${prompt.id}">生成图片</span>
                        </button>
                        <div class="image-count-control">
                            <label>生成数量:</label>
                            <input type="number" class="number-input-sm" value="${prompt.image_count}" min="1" max="10"
                                   onchange="app.updateCharGenImageCount('${prompt.id}', this.value)">
                        </div>
                    </div>

                    <!-- 单独的进度显示区域 -->
                    <div class="single-progress-container" id="singleProgress_${prompt.id}" style="display: none;">
                        <div class="progress-info">
                            <div class="progress-text" id="singleProgressText_${prompt.id}">准备生成...</div>
                            <div class="progress-time" id="singleProgressTime_${prompt.id}">执行时间: 0秒</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="singleProgressFill_${prompt.id}"></div>
                        </div>
                    </div>

                    <div class="generated-images" id="charGenImages_${prompt.id}">
                        <!-- 生成的图片将在这里显示 -->
                    </div>
                </div>
            </div>
        `;

        // 渲染角色选择框
        setTimeout(() => {
            this.renderCharGenCharacterSelection(prompt.id, prompt.selected_characters);
            this.renderCharGenImages(prompt.id, prompt.generated_images);
        }, 0);

        return div;
    }

    renderCharGenCharacterSelection(promptId, selectedCharacters) {
        const container = document.getElementById(`charGenCharacters_${promptId}`);
        if (!container) return;

        container.innerHTML = '';

        if (!this.characters || this.characters.length === 0) {
            container.innerHTML = '<div class="no-characters-message"><i class="fas fa-info-circle"></i> 暂无角色配置，请先在"角色参考图配置"中添加角色</div>';
            return;
        }

        // 创建角色服装选择容器
        const selectionContainer = document.createElement('div');
        selectionContainer.className = 'chargen-character-outfit-selection';

        // 添加选择计数
        const countHeader = document.createElement('div');
        countHeader.className = 'chargen-selection-header';
        countHeader.innerHTML = `
            <span class="selection-label">选择角色和服装:</span>
            <span class="selection-count">已选择 <span id="charGenSelectionCount_${promptId}">${selectedCharacters.length}</span>/5</span>
        `;
        selectionContainer.appendChild(countHeader);

        // 按角色分组显示
        this.characters.forEach(character => {
            const characterCode = character.character_code || '';
            const characterName = character.character_name || '未命名角色';
            const outfits = character.outfits || [];

            const characterGroup = document.createElement('div');
            characterGroup.className = 'chargen-character-group';

            characterGroup.innerHTML = `
                <div class="chargen-character-header">
                    <div class="character-info">
                        <i class="fas fa-user"></i>
                        <span class="character-name">${characterName}</span>
                        <span class="character-code">(${characterCode})</span>
                    </div>
                    <div class="outfit-count">${outfits.length} 套服装</div>
                </div>
                <div class="chargen-outfits-grid" id="charGenOutfits_${promptId}_${characterCode}">
                    <!-- 服装选项将在这里生成 -->
                </div>
            `;

            selectionContainer.appendChild(characterGroup);

            // 渲染服装选项
            const outfitsGrid = characterGroup.querySelector('.chargen-outfits-grid');
            if (outfits.length === 0) {
                outfitsGrid.innerHTML = `
                    <div class="no-outfits-chargen">
                        <i class="fas fa-tshirt"></i>
                        <p>该角色暂无服装配置</p>
                    </div>
                `;
            } else {
                outfits.forEach(outfit => {
                    const selectionId = `${characterCode}:${outfit.outfit_id}`;
                    const isSelected = selectedCharacters.includes(selectionId);

                    const outfitCard = document.createElement('div');
                    outfitCard.className = `chargen-outfit-card ${isSelected ? 'selected' : ''}`;
                    outfitCard.onclick = () => this.toggleCharGenCharacterOutfit(promptId, selectionId);

                    outfitCard.innerHTML = `
                        <div class="outfit-image-small">
                            ${outfit.image_url ?
                                `<img src="${outfit.image_url}" alt="${outfit.outfit_name}">` :
                                `<div class="no-image-small"><i class="fas fa-tshirt"></i></div>`
                            }
                        </div>
                        <div class="outfit-info-small">
                            <div class="outfit-name-small">${outfit.outfit_name}</div>
                        </div>
                        ${isSelected ? '<div class="selection-indicator-small"><i class="fas fa-check"></i></div>' : ''}
                    `;

                    outfitsGrid.appendChild(outfitCard);
                });
            }
        });

        container.appendChild(selectionContainer);

        // 添加提示信息
        const hint = document.createElement('div');
        hint.className = 'character-selection-hint';
        hint.innerHTML = '<small><i class="fas fa-lightbulb"></i> <strong>提示：</strong> 最多可选择5个角色服装组合作为参考图</small>';
        container.appendChild(hint);
    }

    renderCharGenImages(promptId, images) {
        const container = document.getElementById(`charGenImages_${promptId}`);
        if (!container) return;

        container.innerHTML = '';

        if (!images || images.length === 0) {
            return;
        }

        // 创建图片网格容器
        const imagesGrid = document.createElement('div');
        imagesGrid.className = 'chargen-images-grid';
        imagesGrid.id = `charGenImagesGrid_${promptId}`;

        images.forEach((image, index) => {
            const imageDiv = document.createElement('div');
            imageDiv.className = 'chargen-image-item';
            imageDiv.innerHTML = `
                <img src="${image.url}" alt="生成的角色图片 ${index + 1}"
                     onclick="app.showCharGenImagePreview('${promptId}', ${index})"
                     style="cursor: pointer;">
                <div class="chargen-image-info">
                    <span>图片 ${index + 1}</span>
                    ${image.local_path ? `<small>已保存</small>` : ''}
                </div>
            `;
            imagesGrid.appendChild(imageDiv);
        });

        container.appendChild(imagesGrid);
    }

    toggleCharGenPrompt(promptId) {
        const prompt = this.charGenPrompts.find(p => p.id === promptId);
        if (prompt) {
            prompt.enabled = !prompt.enabled;
            this.updateCharGenButtons();
            this.saveCharGenData();
        }
    }

    removeCharGenPrompt(promptId) {
        if (confirm('确定要删除这个角色提示词吗？')) {
            this.charGenPrompts = this.charGenPrompts.filter(p => p.id !== promptId);
            this.renderCharGenPrompts();
            this.updateCharGenButtons();
            this.saveCharGenData();
        }
    }

    updateCharGenPrompt(promptId, newPrompt) {
        const prompt = this.charGenPrompts.find(p => p.id === promptId);
        if (prompt) {
            prompt.prompt = newPrompt;
            this.saveCharGenData();
        }
    }

    updateCharGenImageCount(promptId, newCount) {
        const prompt = this.charGenPrompts.find(p => p.id === promptId);
        if (prompt) {
            prompt.image_count = parseInt(newCount) || 1;
            this.saveCharGenData();
        }
    }

    applyCharGenImageCountToAll() {
        const globalImageCount = parseInt(document.getElementById('charGenImageCount').value) || 1;

        if (!this.charGenPrompts || this.charGenPrompts.length === 0) {
            this.showError('没有角色提示词可以应用');
            return;
        }

        // 更新所有角色提示词的生图数量
        this.charGenPrompts.forEach(prompt => {
            prompt.image_count = globalImageCount;
        });

        // 重新渲染以更新显示
        this.renderCharGenPrompts();
        this.saveCharGenData();

        this.addLog(`✅ 已将生图数量 ${globalImageCount} 应用到所有 ${this.charGenPrompts.length} 个角色提示词`);
    }

    toggleCharGenCharacter(promptId, characterId) {
        // 保留旧方法以兼容性，但现在使用新的选择方式
        console.warn('toggleCharGenCharacter is deprecated, use toggleCharGenCharacterOutfit instead');
    }

    toggleCharGenCharacterOutfit(promptId, selectionId) {
        const prompt = this.charGenPrompts.find(p => p.id === promptId);
        if (!prompt) return;

        const index = prompt.selected_characters.indexOf(selectionId);
        if (index > -1) {
            prompt.selected_characters.splice(index, 1);
        } else {
            if (prompt.selected_characters.length >= 5) {
                this.showError('最多只能选择5个角色服装组合');
                return;
            }
            prompt.selected_characters.push(selectionId);
        }

        // 更新选择计数显示
        const countElement = document.getElementById(`charGenSelectionCount_${promptId}`);
        if (countElement) {
            countElement.textContent = prompt.selected_characters.length;
        }

        // 更新选择状态显示
        this.updateCharGenSelectionDisplay(promptId);

        // 生成参考图说明
        this.generateCharGenReferenceText(prompt);
        this.renderCharGenPrompts();
        this.saveCharGenData();
    }

    updateCharGenSelectionDisplay(promptId) {
        const prompt = this.charGenPrompts.find(p => p.id === promptId);
        if (!prompt) return;

        // 更新所有服装卡片的选择状态
        document.querySelectorAll('.chargen-outfit-card').forEach(card => {
            const selectionId = card.onclick.toString().match(/'([^']+)'/)?.[1];
            if (selectionId) {
                const isSelected = prompt.selected_characters.includes(selectionId);

                if (isSelected) {
                    card.classList.add('selected');
                    if (!card.querySelector('.selection-indicator-small')) {
                        card.innerHTML += '<div class="selection-indicator-small"><i class="fas fa-check"></i></div>';
                    }
                } else {
                    card.classList.remove('selected');
                    const indicator = card.querySelector('.selection-indicator-small');
                    if (indicator) {
                        indicator.remove();
                    }
                }
            }
        });
    }

    generateCharGenReferenceText(prompt) {
        if (!prompt.selected_characters || prompt.selected_characters.length === 0) {
            prompt.reference_text = '';
            return;
        }

        let referenceText = '';
        prompt.selected_characters.forEach((selectionId, index) => {
            let character = null;

            // 检查是否为新格式（包含冒号）
            if (selectionId.includes(':')) {
                // 新格式：characterCode:outfitId
                const [characterCode, outfitId] = selectionId.split(':');
                character = this.characters.find(c => c.character_code === characterCode);
            } else {
                // 旧格式：直接是角色ID
                character = this.characters.find(c => c.id === selectionId);
            }

            if (character) {
                const position = ['第一', '第二', '第三', '第四', '第五'][index];
                const displayName = character.display_type === 'code' ?
                    (character.character_code || character.code) :
                    (character.character_name || character.name);
                let characterText = `${displayName}的角色形象:请严格参考我提供的${position}张图片(角色参考图)来塑造角色。`;

                // 如果角色有核心特征，添加到参考图说明中
                if (character.core_features && character.core_features.trim()) {
                    characterText += `角色不可变的核心特征=【${character.core_features.trim()}】`;
                }

                referenceText += characterText;
            }
        });

        prompt.reference_text = referenceText;
    }

    updateCharGenButtons() {
        const hasPrompts = this.charGenPrompts && this.charGenPrompts.length > 0;
        const hasEnabledPrompts = hasPrompts && this.charGenPrompts.some(p => p.enabled);

        document.getElementById('charGenGenerateAllBtn').disabled = !hasEnabledPrompts;
        document.getElementById('charGenSaveCacheBtn').disabled = !hasPrompts;
        document.getElementById('charGenClearDataBtn').disabled = !hasPrompts;
    }

    async generateAllCharacterImages() {
        const enabledPrompts = this.charGenPrompts.filter(p => p.enabled);
        if (enabledPrompts.length === 0) {
            this.showError('没有启用的角色提示词');
            return;
        }

        // 验证配置
        const serviceId = document.getElementById('charGenModelService').value;
        const modelName = document.getElementById('charGenImageModel').value;
        const saveDir = document.getElementById('charGenSaveDir').value.trim();
        const imageCount = parseInt(document.getElementById('charGenImageCount').value) || 1;

        if (!serviceId || !modelName) {
            this.showError('请选择模型服务和生图模型');
            return;
        }

        if (!saveDir) {
            this.showError('请设置保存目录');
            return;
        }

        // 获取超时配置
        const timeoutConfig = await this.getTimeoutConfig(serviceId);
        const timeoutSeconds = timeoutConfig || 300;

        try {
            // 显示详细的角色批量进度
            this.showCharGenBatchProgress(enabledPrompts, timeoutSeconds);

            this.addLog(`开始批量生成角色图片: ${enabledPrompts.length}个角色, 服务=${serviceId}, 模型=${modelName}`);

            // 顺序生成角色图片（避免并发过多）
            let completedCount = 0;
            const results = [];

            for (const prompt of enabledPrompts) {
                try {
                    // 更新当前角色状态
                    this.updateCharGenBatchItemStatus(prompt.id, 'generating', '生成中...');

                    const images = await this.generateCharGenImages(prompt.id, false); // false表示不显示单独的进度

                    completedCount++;
                    results.push({ promptId: prompt.id, images, success: true });

                    // 更新角色状态为成功
                    this.updateCharGenBatchItemStatus(prompt.id, 'success', `生成成功 (${images.length}张)`);

                    // 更新总进度
                    this.updateCharGenProgress((completedCount / enabledPrompts.length) * 100,
                                            `已完成 ${completedCount}/${enabledPrompts.length} 个角色`);

                } catch (error) {
                    this.addLog(`❌ 角色 ${prompt.id} 生成失败: ${error.message}`);
                    completedCount++;
                    results.push({ promptId: prompt.id, error: error.message, success: false });

                    // 更新角色状态为失败
                    this.updateCharGenBatchItemStatus(prompt.id, 'failed', `生成失败: ${error.message}`);
                }
            }

            // 统计结果
            const successCount = results.filter(r => r.success).length;
            const failedCount = results.filter(r => !r.success).length;

            this.addLog(`✅ 角色图片批量生成完成！成功: ${successCount}, 失败: ${failedCount}`);
            this.saveCharGenData();

            // 3秒后隐藏进度
            setTimeout(() => {
                this.hideCharGenBatchProgress();
            }, 3000);

        } catch (error) {
            this.hideCharGenBatchProgress();
            this.showError('批量生成失败: ' + error.message);
        }
    }

    async generateCharGenImages(promptId, showProgress = true) {
        const prompt = this.charGenPrompts.find(p => p.id === promptId);
        if (!prompt) {
            this.showError('找不到指定的角色提示词');
            return [];
        }

        if (!prompt.prompt.trim()) {
            this.showError('请输入角色提示词');
            return [];
        }

        // 验证配置
        const serviceId = document.getElementById('charGenModelService').value;
        const modelName = document.getElementById('charGenImageModel').value;
        const saveDir = document.getElementById('charGenSaveDir').value.trim();
        const imageCount = parseInt(document.getElementById('charGenImageCount').value) || 1;

        if (!serviceId || !modelName) {
            this.showError('请选择模型服务和生图模型');
            return [];
        }

        if (!saveDir) {
            this.showError('请设置保存目录');
            return [];
        }

        // 获取超时配置
        const timeoutConfig = await this.getTimeoutConfig(serviceId);
        const timeoutSeconds = timeoutConfig || 300; // 默认300秒

        // 开始执行时间统计
        const startTime = Date.now();
        let timeInterval = null;

        try {
            // 设置按钮加载状态
            this.setGenerateButtonLoading(promptId, true);

            // 显示单独的进度
            this.showSingleProgress(promptId, timeoutSeconds);

            // 开始时间统计
            timeInterval = this.startTimeTracking(promptId, startTime, timeoutSeconds);

            if (showProgress) {
                this.showCharGenProgress();
                this.updateCharGenProgress(10, '准备生成角色图片...');
            }

            // 更新单独进度
            this.updateSingleProgress(promptId, 10, '准备生成角色图片...');

            // 生成时间戳文件名前缀
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);

            // 准备生成请求，使用与一键生成故事相同的接口格式
            const generateRequest = {
                service_id: serviceId,
                model: modelName,
                prompt: prompt.prompt,
                selected_characters: prompt.selected_characters || [],
                image_count: imageCount,
                save_directory: saveDir,
                excel_filename: `character_${timestamp}`,
                scene_number: `char_${promptId.slice(-4)}`
            };

            if (showProgress) {
                this.updateCharGenProgress(30, '正在生成图片...');
            }

            // 更新单独进度
            this.updateSingleProgress(promptId, 30, '正在生成图片...');

            // 调用生成API，使用与一键生成故事相同的接口
            const result = await this.apiCall('/api/generate/single', 'POST', generateRequest);

            if (showProgress) {
                this.updateCharGenProgress(90, '处理生成结果...');
            }

            // 更新单独进度
            this.updateSingleProgress(promptId, 90, '处理生成结果...');

            if (result.success && result.generated_images) {
                // 更新角色提示词的生成图片
                prompt.generated_images = result.generated_images;

                // 重新渲染图片
                this.renderCharGenImages(promptId, result.generated_images);

                if (showProgress) {
                    this.updateCharGenProgress(100, '生成完成！');
                    setTimeout(() => this.hideCharGenProgress(), 1000);
                }

                // 完成单独进度
                this.updateSingleProgress(promptId, 100, '生成完成！');
                const totalTime = Math.floor((Date.now() - startTime) / 1000);
                this.addLog(`✅ 角色图片生成成功，共 ${result.generated_images.length} 张，耗时 ${totalTime} 秒`);
                this.saveCharGenData();

                // 延迟隐藏进度和恢复按钮
                setTimeout(() => {
                    this.hideSingleProgress(promptId);
                    this.setGenerateButtonLoading(promptId, false);
                }, 2000);

                return result.generated_images;
            } else {
                throw new Error(result.error || '生成失败');
            }

        } catch (error) {
            // 清理定时器和状态
            if (timeInterval) {
                clearInterval(timeInterval);
            }
            this.setGenerateButtonLoading(promptId, false);
            this.hideSingleProgress(promptId);

            if (showProgress) {
                this.hideCharGenProgress();
            }

            const totalTime = Math.floor((Date.now() - startTime) / 1000);
            this.addLog(`❌ 角色图片生成失败: ${error.message}，耗时 ${totalTime} 秒`);
            this.showError('生成角色图片失败: ' + error.message);
            return [];
        } finally {
            // 确保清理定时器
            if (timeInterval) {
                clearInterval(timeInterval);
            }
        }
    }

    showCharGenProgress() {
        document.getElementById('charGenProgressContainer').style.display = 'block';
    }

    // 设置生成按钮的加载状态
    setGenerateButtonLoading(promptId, isLoading) {
        const btn = document.getElementById(`generateBtn_${promptId}`);
        const icon = document.getElementById(`generateIcon_${promptId}`);
        const text = document.getElementById(`generateText_${promptId}`);

        if (!btn || !icon || !text) return;

        if (isLoading) {
            btn.classList.add('loading');
            btn.disabled = true;
            icon.className = 'fas fa-spinner';
            text.textContent = '生成中...';
        } else {
            btn.classList.remove('loading');
            btn.disabled = false;
            icon.className = 'fas fa-image';
            text.textContent = '生成图片';
        }
    }

    // 显示单独的进度
    showSingleProgress(promptId, timeoutSeconds) {
        const container = document.getElementById(`singleProgress_${promptId}`);
        if (container) {
            container.style.display = 'block';
            this.updateSingleProgress(promptId, 0, `开始生成... (超时时间: ${timeoutSeconds}秒)`);
        }
    }

    // 隐藏单独的进度
    hideSingleProgress(promptId) {
        const container = document.getElementById(`singleProgress_${promptId}`);
        if (container) {
            container.style.display = 'none';
        }
    }

    // 更新单独的进度
    updateSingleProgress(promptId, percentage, message) {
        const fill = document.getElementById(`singleProgressFill_${promptId}`);
        const text = document.getElementById(`singleProgressText_${promptId}`);

        if (fill) {
            fill.style.width = `${percentage}%`;
        }
        if (text) {
            text.textContent = message;
        }
    }

    // 开始时间统计
    startTimeTracking(promptId, startTime, timeoutSeconds) {
        const timeElement = document.getElementById(`singleProgressTime_${promptId}`);
        if (!timeElement) return null;

        return setInterval(() => {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const warningThreshold = timeoutSeconds * 0.8; // 80%时显示警告

            let timeClass = '';
            if (elapsed >= timeoutSeconds) {
                timeClass = 'timeout-error';
            } else if (elapsed >= warningThreshold) {
                timeClass = 'timeout-warning';
            }

            timeElement.className = `progress-time ${timeClass}`;
            timeElement.textContent = `执行时间: ${elapsed}秒 (超时时间: ${timeoutSeconds}秒)`;
        }, 1000);
    }

    // 获取超时配置
    async getTimeoutConfig(serviceId) {
        try {
            const response = await this.apiCall('/api/config/models', 'GET');
            if (response && response.services) {
                const service = response.services.find(s => s.id == serviceId);
                return service ? service.timeout : 300;
            }
        } catch (error) {
            console.warn('获取超时配置失败:', error);
        }
        return 300; // 默认300秒
    }

    hideCharGenProgress() {
        document.getElementById('charGenProgressContainer').style.display = 'none';
    }

    updateCharGenProgress(percentage, text) {
        document.getElementById('charGenProgressFill').style.width = percentage + '%';
        document.getElementById('charGenProgressText').textContent = text;
    }

    saveCharGenCache() {
        try {
            if (!this.charGenPrompts || this.charGenPrompts.length === 0) {
                this.showError('没有角色数据需要保存');
                return;
            }

            this.saveCharGenData();

            // 统计信息
            const totalPrompts = this.charGenPrompts.length;
            const enabledPrompts = this.charGenPrompts.filter(p => p.enabled).length;
            let totalImages = 0;
            let totalCharacters = 0;

            this.charGenPrompts.forEach(prompt => {
                if (prompt.generated_images) {
                    totalImages += prompt.generated_images.length;
                }
                if (prompt.selected_characters) {
                    totalCharacters += prompt.selected_characters.length;
                }
            });

            this.addLog('✅ 角色生成缓存保存成功！');
            this.addLog(`📝 ${totalPrompts}个角色提示词（${enabledPrompts}个启用）`);
            this.addLog(`👥 ${totalCharacters}个参考角色选择`);
            this.addLog(`🖼️ ${totalImages}张生成图片`);

        } catch (error) {
            this.showError('保存角色生成缓存失败: ' + error.message);
        }
    }

    clearCharGenData() {
        if (confirm('确定要清空所有角色生成数据吗？此操作不可恢复。')) {
            this.charGenPrompts = [];
            this.renderCharGenPrompts();
            this.updateCharGenButtons();
            this.saveCharGenData();
            this.addLog('角色生成数据已清空');
        }
    }

    saveCharGenData() {
        try {
            const data = {
                charGenPrompts: this.charGenPrompts || [],
                timestamp: new Date().toISOString()
            };

            localStorage.setItem('auto_image_chargen_data', JSON.stringify(data));
        } catch (error) {
            console.error('保存角色生成数据失败:', error);
        }
    }

    restoreCharGenData() {
        try {
            const savedData = localStorage.getItem('auto_image_chargen_data');
            if (savedData) {
                const data = JSON.parse(savedData);
                this.charGenPrompts = data.charGenPrompts || [];

                if (this.charGenPrompts.length > 0) {
                    this.renderCharGenPrompts();
                    this.updateCharGenButtons();

                    const saveTime = new Date(data.timestamp).toLocaleString();
                    this.addLog(`已恢复角色生成数据 (保存时间: ${saveTime})`);
                }
            } else {
                this.charGenPrompts = [];
                this.renderCharGenPrompts();
                this.updateCharGenButtons();
            }
        } catch (error) {
            console.error('恢复角色生成数据失败:', error);
            this.charGenPrompts = [];
            this.renderCharGenPrompts();
            this.updateCharGenButtons();
        }
    }



    showCharGenImagePreview(promptId, imageIndex) {
        const prompt = this.charGenPrompts.find(p => p.id === promptId);
        if (!prompt || !prompt.generated_images || imageIndex < 0 || imageIndex >= prompt.generated_images.length) {
            return;
        }

        const image = prompt.generated_images[imageIndex];

        // 设置当前角色生成上下文，用于模态框导航
        this.currentCharGenContext = {
            promptId: promptId,
            imageIndex: imageIndex
        };

        this.showImagePreview(image.url);
    }

    async browseCharGenDirectory() {
        try {
            const currentDir = document.getElementById('charGenSaveDir').value;

            this.addLog('正在打开文件夹选择对话框...');

            const response = await this.apiCall('/api/select-directory', 'POST', {
                current_directory: currentDir
            });

            if (response.success && response.directory) {
                document.getElementById('charGenSaveDir').value = response.directory;
                this.addLog(`角色生成保存目录已更新: ${response.directory}`);

                // 同步更新一键生成故事模块的保存目录
                const storyModuleSaveDir = document.getElementById('saveDirectory');
                if (storyModuleSaveDir) {
                    storyModuleSaveDir.value = response.directory;
                }
            } else if (response.message) {
                this.addLog(response.message);
            } else {
                this.showError('选择目录失败: ' + (response.error || '未知错误'));
            }
        } catch (error) {
            this.showError('选择目录失败: ' + error.message);
            // 如果API调用失败，回退到原来的输入方式
            this.addLog('回退到手动输入模式...');
            const currentDir = document.getElementById('charGenSaveDir').value;
            const newDir = prompt('请输入保存目录路径：', currentDir);

            if (newDir && newDir.trim() !== '') {
                document.getElementById('charGenSaveDir').value = newDir.trim();
                this.addLog(`角色生成保存目录已更新: ${newDir.trim()}`);

                // 同步更新一键生成故事模块的保存目录
                const storyModuleSaveDir = document.getElementById('saveDirectory');
                if (storyModuleSaveDir) {
                    storyModuleSaveDir.value = newDir.trim();
                }
            }
        }
    }

    // Excel转换相关方法
    async loadExcelConfig() {
        try {
            // 设置默认保存目录，与一键生成故事模块保持一致
            const storyModuleSaveDir = document.getElementById('saveDirectory');
            if (storyModuleSaveDir && storyModuleSaveDir.value) {
                // 如果一键生成故事模块已有保存目录，使用相同的目录
                document.getElementById('excelSaveDir').value = storyModuleSaveDir.value;
            } else {
                // 否则尝试从配置中获取
                try {
                    const config = await this.apiCall('/api/config', 'GET');
                    if (config.settings && config.settings.save_directory) {
                        document.getElementById('excelSaveDir').value = config.settings.save_directory;
                    }
                } catch (error) {
                    // 如果获取配置失败，使用默认值
                    console.log('使用默认保存目录');
                }
            }
        } catch (error) {
            this.showError('加载Excel转换配置失败: ' + error.message);
        }
    }

    async browseExcelDirectory() {
        try {
            const currentDir = document.getElementById('excelSaveDir').value;

            this.addLog('正在打开文件夹选择对话框...');

            const response = await this.apiCall('/api/select-directory', 'POST', {
                current_directory: currentDir
            });

            if (response.success && response.directory) {
                document.getElementById('excelSaveDir').value = response.directory;
                this.addLog(`Excel转换保存目录已更新: ${response.directory}`);

                // 同步更新一键生成故事模块的保存目录
                const storyModuleSaveDir = document.getElementById('saveDirectory');
                if (storyModuleSaveDir) {
                    storyModuleSaveDir.value = response.directory;
                }

                // 同步更新角色形象生成模块的保存目录
                const charGenSaveDir = document.getElementById('charGenSaveDir');
                if (charGenSaveDir) {
                    charGenSaveDir.value = response.directory;
                }
            } else if (response.message) {
                this.addLog(response.message);
            } else {
                this.showError('选择目录失败: ' + (response.error || '未知错误'));
            }
        } catch (error) {
            this.showError('选择目录失败: ' + error.message);
            // 如果API调用失败，回退到原来的输入方式
            this.addLog('回退到手动输入模式...');
            const currentDir = document.getElementById('excelSaveDir').value;
            const newDir = prompt('请输入保存目录路径：', currentDir);

            if (newDir && newDir.trim() !== '') {
                document.getElementById('excelSaveDir').value = newDir.trim();
                this.addLog(`Excel转换保存目录已更新: ${newDir.trim()}`);

                // 同步更新其他模块的保存目录
                const storyModuleSaveDir = document.getElementById('saveDirectory');
                if (storyModuleSaveDir) {
                    storyModuleSaveDir.value = newDir.trim();
                }

                const charGenSaveDir = document.getElementById('charGenSaveDir');
                if (charGenSaveDir) {
                    charGenSaveDir.value = newDir.trim();
                }
            }
        }
    }

    handleTxtFileSelect(file) {
        if (!file) return;

        // 验证文件类型
        if (!file.name.toLowerCase().endsWith('.txt')) {
            this.showError('请选择TXT格式的文件');
            return;
        }

        // 验证文件大小 (限制为10MB)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            this.showError('文件大小不能超过10MB');
            return;
        }

        this.processTxtFile(file);
    }

    async processTxtFile(file) {
        try {
            // 显示进度条
            document.getElementById('txtUploadProgress').style.display = 'block';
            document.getElementById('txtUploadResult').style.display = 'none';
            document.getElementById('txtUploadProgressText').textContent = '正在读取文件...';
            document.getElementById('txtUploadProgressFill').style.width = '20%';

            // 读取文件内容
            const fileContent = await this.readFileAsText(file);

            document.getElementById('txtUploadProgressText').textContent = '正在分析文件内容...';
            document.getElementById('txtUploadProgressFill').style.width = '50%';

            // 验证文件内容是否可以转换为Excel
            const canConvert = this.validateTxtForExcel(fileContent);

            if (!canConvert.valid) {
                throw new Error(canConvert.error);
            }

            document.getElementById('txtUploadProgressText').textContent = '正在转换为Excel...';
            document.getElementById('txtUploadProgressFill').style.width = '80%';

            // 获取保存目录
            const saveDir = document.getElementById('excelSaveDir').value;
            if (!saveDir) {
                throw new Error('请先设置保存目录');
            }

            // 调用后端API转换文件
            const response = await this.apiCall('/api/excel/convert-txt', 'POST', {
                file_content: fileContent,
                original_filename: file.name,
                save_directory: saveDir
            });

            document.getElementById('txtUploadProgressFill').style.width = '100%';
            document.getElementById('txtUploadProgressText').textContent = '转换完成！';

            // 隐藏进度条，显示结果
            setTimeout(() => {
                document.getElementById('txtUploadProgress').style.display = 'none';
                this.showTxtProcessResult(response);
            }, 500);

        } catch (error) {
            document.getElementById('txtUploadProgress').style.display = 'none';
            this.showError('处理TXT文件失败: ' + error.message);
        }
    }

    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('读取文件失败'));
            reader.readAsText(file, 'utf-8');
        });
    }

    validateTxtForExcel(content) {
        try {
            // 检查是否包含CSV格式的内容
            const lines = content.trim().split('\n');

            if (lines.length < 2) {
                return { valid: false, error: '文件内容太少，无法转换为Excel' };
            }

            // 检查是否有引号包围的字段（CSV格式）
            const hasQuotedFields = lines.some(line => line.includes('"'));

            if (!hasQuotedFields) {
                return { valid: false, error: '文件格式不正确，应该包含用引号包围的字段' };
            }

            // 检查是否包含必要的列（分镜序号和文生图prompt）
            const firstLine = lines[0].toLowerCase();
            const hasRequiredColumns = firstLine.includes('分镜序号') || firstLine.includes('文生图prompt');

            if (!hasRequiredColumns) {
                return { valid: false, error: '文件应该包含"分镜序号"和"文生图prompt"列' };
            }

            return { valid: true };
        } catch (error) {
            return { valid: false, error: '文件格式验证失败: ' + error.message };
        }
    }

    showTxtProcessResult(response) {
        const resultDiv = document.getElementById('txtUploadResult');
        const resultText = document.getElementById('txtProcessResult');
        const downloadBtn = document.getElementById('downloadExcelBtn');
        const openDirBtn = document.getElementById('openExcelDirBtn');
        const clearBtn = document.getElementById('clearExcelDataBtn');

        if (response.success) {
            resultText.innerHTML = `
                <div class="success-message">
                    <i class="fas fa-check-circle"></i>
                    <strong>转换成功！</strong><br>
                    Excel文件已保存到: ${response.file_path}<br>
                    包含 ${response.row_count} 行数据
                </div>
            `;

            downloadBtn.style.display = 'inline-block';
            openDirBtn.style.display = 'inline-block';
            clearBtn.style.display = 'inline-block';

            // 保存文件路径供下载使用
            this.currentExcelFilePath = response.file_path;

            this.addLog(`TXT文件转换成功，Excel文件保存到: ${response.file_path}`);
        } else {
            resultText.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <strong>转换失败：</strong><br>
                    ${response.error || '未知错误'}
                </div>
            `;

            downloadBtn.style.display = 'none';
            openDirBtn.style.display = 'none';
        }

        resultDiv.style.display = 'block';
    }

    async downloadGeneratedExcel() {
        if (!this.currentExcelFilePath) {
            this.showError('没有可下载的Excel文件');
            return;
        }

        try {
            // 创建下载链接
            const response = await fetch('/api/excel/download', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    file_path: this.currentExcelFilePath
                })
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = this.currentExcelFilePath.split('\\').pop() || 'converted.xlsx';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                this.addLog('Excel文件下载完成');
            } else {
                throw new Error('下载失败');
            }
        } catch (error) {
            this.showError('下载Excel文件失败: ' + error.message);
        }
    }

    async openExcelDirectory() {
        const saveDir = document.getElementById('excelSaveDir').value;
        if (!saveDir) {
            this.showError('保存目录未设置');
            return;
        }

        try {
            const response = await this.apiCall('/api/open-directory', 'POST', {
                directory: saveDir
            });

            if (response.success) {
                this.addLog(`已打开目录: ${saveDir}`);
            } else {
                this.showError('打开目录失败: ' + (response.error || '未知错误'));
            }
        } catch (error) {
            this.showError('打开目录失败: ' + error.message);
        }
    }

    clearExcelData() {
        // 清空结果显示
        document.getElementById('txtUploadResult').style.display = 'none';
        document.getElementById('txtUploadProgress').style.display = 'none';
        document.getElementById('clearExcelDataBtn').style.display = 'none';

        // 重置文件输入
        document.getElementById('txtFile').value = '';

        // 清空当前文件路径
        this.currentExcelFilePath = null;

        this.addLog('Excel转换数据已清空');
    }

    // ===== 单个分镜进度显示相关函数 =====

    showSceneProgress(sceneIndex, imageCount, timeoutSeconds) {
        const progressContainer = document.getElementById(`sceneProgress_${sceneIndex}`);
        const progressDetails = document.getElementById(`sceneProgressDetails_${sceneIndex}`);
        const progressTime = document.getElementById(`sceneProgressTime_${sceneIndex}`);

        if (!progressContainer || !progressDetails) return;

        // 显示进度容器
        progressContainer.style.display = 'block';

        // 生成进度项
        let progressHTML = '';
        for (let i = 1; i <= imageCount; i++) {
            progressHTML += `
                <div class="image-progress-item" id="imageProgress_${sceneIndex}_${i}">
                    <div class="image-progress-info">
                        <div class="image-progress-label">图片 ${i}</div>
                        <div class="image-progress-status" id="imageStatus_${sceneIndex}_${i}">等待生成...</div>
                    </div>
                    <div class="image-progress-time" id="imageTime_${sceneIndex}_${i}">
                        耗时: 0秒 / 超时: ${timeoutSeconds}秒
                    </div>
                </div>
            `;
        }
        progressDetails.innerHTML = progressHTML;

        // 开始时间统计
        const startTime = Date.now();
        this.sceneProgressTimers = this.sceneProgressTimers || {};
        this.sceneProgressTimers[sceneIndex] = setInterval(() => {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const warningThreshold = timeoutSeconds * 0.8;

            let timeClass = '';
            if (elapsed >= timeoutSeconds) {
                timeClass = 'error';
            } else if (elapsed >= warningThreshold) {
                timeClass = 'warning';
            }

            progressTime.className = `progress-time ${timeClass}`;
            progressTime.textContent = `总耗时: ${elapsed}秒 (超时时间: ${timeoutSeconds}秒)`;

            // 更新每个图片的时间显示
            for (let i = 1; i <= imageCount; i++) {
                const timeElement = document.getElementById(`imageTime_${sceneIndex}_${i}`);
                if (timeElement && !timeElement.dataset.completed) {
                    timeElement.className = `image-progress-time ${timeClass}`;
                    timeElement.textContent = `耗时: ${elapsed}秒 / 超时: ${timeoutSeconds}秒`;
                }
            }
        }, 1000);

        // 模拟进度更新（实际应该从后端获取）
        this.simulateSceneProgress(sceneIndex, imageCount);
    }

    simulateSceneProgress(sceneIndex, imageCount) {
        // 模拟每个图片的生成进度
        for (let i = 1; i <= imageCount; i++) {
            setTimeout(() => {
                const statusElement = document.getElementById(`imageStatus_${sceneIndex}_${i}`);
                if (statusElement) {
                    statusElement.textContent = '生成中...';
                    statusElement.className = 'image-progress-status generating';
                }
            }, i * 1000); // 每秒开始一个图片的生成
        }
    }

    updateSceneProgressSuccess(sceneIndex, actualCount) {
        // 更新所有图片为成功状态
        const progressDetails = document.getElementById(`sceneProgressDetails_${sceneIndex}`);
        if (progressDetails) {
            const statusElements = progressDetails.querySelectorAll('.image-progress-status');
            const timeElements = progressDetails.querySelectorAll('.image-progress-time');

            statusElements.forEach((element, index) => {
                if (index < actualCount) {
                    element.textContent = '生成成功';
                    element.className = 'image-progress-status success';
                }
            });

            timeElements.forEach(element => {
                element.dataset.completed = 'true';
                element.className = 'image-progress-time';
            });
        }

        // 停止计时器
        if (this.sceneProgressTimers && this.sceneProgressTimers[sceneIndex]) {
            clearInterval(this.sceneProgressTimers[sceneIndex]);
            delete this.sceneProgressTimers[sceneIndex];
        }
    }

    updateSceneProgressError(sceneIndex, errorMessage) {
        // 更新所有图片为失败状态
        const progressDetails = document.getElementById(`sceneProgressDetails_${sceneIndex}`);
        if (progressDetails) {
            const statusElements = progressDetails.querySelectorAll('.image-progress-status');
            const timeElements = progressDetails.querySelectorAll('.image-progress-time');

            statusElements.forEach(element => {
                element.textContent = `生成失败: ${errorMessage}`;
                element.className = 'image-progress-status failed';
            });

            timeElements.forEach(element => {
                element.dataset.completed = 'true';
                element.className = 'image-progress-time error';
            });
        }

        // 停止计时器
        if (this.sceneProgressTimers && this.sceneProgressTimers[sceneIndex]) {
            clearInterval(this.sceneProgressTimers[sceneIndex]);
            delete this.sceneProgressTimers[sceneIndex];
        }
    }

    hideSceneProgress(sceneIndex) {
        const progressContainer = document.getElementById(`sceneProgress_${sceneIndex}`);
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }

        // 清理计时器
        if (this.sceneProgressTimers && this.sceneProgressTimers[sceneIndex]) {
            clearInterval(this.sceneProgressTimers[sceneIndex]);
            delete this.sceneProgressTimers[sceneIndex];
        }
    }

    setSceneButtonLoading(sceneIndex, loading) {
        const button = document.getElementById(`generateBtn_${sceneIndex}`);
        const icon = document.getElementById(`generateIcon_${sceneIndex}`);
        const text = document.getElementById(`generateText_${sceneIndex}`);

        if (button && icon && text) {
            if (loading) {
                button.classList.add('loading');
                button.disabled = true;
                icon.className = 'fas fa-spinner';
                text.textContent = '生成中...';
            } else {
                button.classList.remove('loading');
                button.disabled = false;
                icon.className = 'fas fa-magic';
                text.textContent = '生成图片';
            }
        }
    }

    // ===== 批量进度显示相关函数 =====

    showBatchProgress(enabledScenes, timeoutSeconds) {
        const progressContainer = document.getElementById('progressContainer');
        const batchProgressDetails = document.getElementById('batchProgressDetails');
        const batchProgressTime = document.getElementById('batchProgressTime');

        if (!progressContainer || !batchProgressDetails) return;

        // 显示进度容器
        progressContainer.style.display = 'block';

        // 生成分镜进度项
        let progressHTML = '';
        enabledScenes.forEach((scene, index) => {
            progressHTML += `
                <div class="batch-scene-item" id="batchScene_${scene.scene_number}">
                    <div class="batch-scene-info">
                        <div class="batch-scene-label">分镜 ${scene.scene_number}</div>
                        <div class="batch-scene-status waiting" id="batchStatus_${scene.scene_number}">等待生成...</div>
                    </div>
                    <div class="batch-scene-progress">
                        <div class="batch-scene-time" id="batchTime_${scene.scene_number}">
                            耗时: 0秒 / 超时: ${timeoutSeconds}秒
                        </div>
                        <div class="batch-scene-images" id="batchImages_${scene.scene_number}">
                            目标: ${scene.image_count}张
                        </div>
                    </div>
                </div>
            `;
        });
        batchProgressDetails.innerHTML = progressHTML;

        // 开始时间统计
        const startTime = Date.now();
        this.batchProgressTimer = setInterval(() => {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const warningThreshold = timeoutSeconds * 0.8;

            let timeClass = '';
            if (elapsed >= timeoutSeconds) {
                timeClass = 'timeout-error';
            } else if (elapsed >= warningThreshold) {
                timeClass = 'timeout-warning';
            }

            batchProgressTime.className = `progress-time ${timeClass}`;
            batchProgressTime.textContent = `总耗时: ${elapsed}秒 (超时时间: ${timeoutSeconds}秒)`;
        }, 1000);

        // 初始化进度条
        this.updateProgress(0, enabledScenes.length, '开始批量生成...');
    }

    async apiCallWithProgress(url, method, data, enabledScenes) {
        // 启动一个简单的进度模拟
        this.simulateBatchProgress(enabledScenes);

        // 执行实际的API调用
        return await this.apiCall(url, method, data);
    }

    simulateBatchProgress(enabledScenes) {
        // 模拟每个分镜的生成进度
        enabledScenes.forEach((scene, index) => {
            const delay = index * 2000; // 每2秒开始一个分镜

            setTimeout(() => {
                const statusElement = document.getElementById(`batchStatus_${scene.scene_number}`);
                if (statusElement) {
                    statusElement.textContent = '生成中...';
                    statusElement.className = 'batch-scene-status generating';
                }

                // 更新总进度
                this.updateProgress(index + 1, enabledScenes.length, `正在生成分镜 ${scene.scene_number}...`);
            }, delay);
        });
    }

    updateBatchProgressComplete(result) {
        // 更新所有分镜为完成状态
        result.results.forEach(sceneResult => {
            const statusElement = document.getElementById(`batchStatus_${sceneResult.scene_number}`);
            const imagesElement = document.getElementById(`batchImages_${sceneResult.scene_number}`);

            if (statusElement) {
                statusElement.textContent = '生成成功';
                statusElement.className = 'batch-scene-status success';
            }

            if (imagesElement) {
                imagesElement.textContent = `完成: ${sceneResult.images.length}张`;
            }
        });

        // 处理失败的分镜
        if (result.failed_scenes) {
            result.failed_scenes.forEach(failedScene => {
                const statusElement = document.getElementById(`batchStatus_${failedScene.scene_number}`);
                if (statusElement) {
                    statusElement.textContent = `生成失败: ${failedScene.error}`;
                    statusElement.className = 'batch-scene-status failed';
                }
            });
        }

        // 停止计时器
        if (this.batchProgressTimer) {
            clearInterval(this.batchProgressTimer);
            this.batchProgressTimer = null;
        }

        // 更新总进度为完成
        this.updateProgress(result.successful_scenes, result.successful_scenes + result.failed_scenes.length,
                          `批量生成完成！成功: ${result.successful_scenes}, 失败: ${result.failed_scenes.length}`);
    }

    updateBatchProgressError(errorMessage) {
        // 更新所有分镜为失败状态
        const statusElements = document.querySelectorAll('[id^="batchStatus_"]');
        statusElements.forEach(element => {
            if (element.className.includes('waiting') || element.className.includes('generating')) {
                element.textContent = `生成失败: ${errorMessage}`;
                element.className = 'batch-scene-status failed';
            }
        });

        // 停止计时器
        if (this.batchProgressTimer) {
            clearInterval(this.batchProgressTimer);
            this.batchProgressTimer = null;
        }
    }

    hideBatchProgress() {
        const progressContainer = document.getElementById('progressContainer');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }

        // 清理计时器
        if (this.batchProgressTimer) {
            clearInterval(this.batchProgressTimer);
            this.batchProgressTimer = null;
        }
    }

    // ===== 角色批量进度显示相关函数 =====

    showCharGenBatchProgress(enabledPrompts, timeoutSeconds) {
        const progressContainer = document.getElementById('charGenProgressContainer');

        if (!progressContainer) return;

        // 显示进度容器
        progressContainer.style.display = 'block';

        // 修改进度容器的HTML结构以支持详细进度
        progressContainer.innerHTML = `
            <div class="progress-header">
                <h4>角色批量生成进度</h4>
                <span class="progress-time" id="charGenBatchProgressTime">准备中...</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="charGenProgressFill"></div>
            </div>
            <div class="progress-text">
                <span id="charGenProgressText">准备中...</span>
                <span id="charGenEstimatedTime"></span>
            </div>
            <div class="batch-progress-details" id="charGenBatchProgressDetails">
                <!-- 动态生成的角色进度 -->
            </div>
        `;

        const batchProgressDetails = document.getElementById('charGenBatchProgressDetails');
        const charGenBatchProgressTime = document.getElementById('charGenBatchProgressTime');

        // 生成角色进度项
        let progressHTML = '';
        enabledPrompts.forEach((prompt, index) => {
            progressHTML += `
                <div class="batch-scene-item" id="charGenBatchItem_${prompt.id}">
                    <div class="batch-scene-info">
                        <div class="batch-scene-label">角色 ${index + 1}</div>
                        <div class="batch-scene-status waiting" id="charGenBatchStatus_${prompt.id}">等待生成...</div>
                    </div>
                    <div class="batch-scene-progress">
                        <div class="batch-scene-time" id="charGenBatchTime_${prompt.id}">
                            耗时: 0秒 / 超时: ${timeoutSeconds}秒
                        </div>
                        <div class="batch-scene-images" id="charGenBatchImages_${prompt.id}">
                            目标: ${prompt.image_count}张
                        </div>
                    </div>
                </div>
            `;
        });
        batchProgressDetails.innerHTML = progressHTML;

        // 开始时间统计
        const startTime = Date.now();
        this.charGenBatchProgressTimer = setInterval(() => {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const warningThreshold = timeoutSeconds * 0.8;

            let timeClass = '';
            if (elapsed >= timeoutSeconds) {
                timeClass = 'timeout-error';
            } else if (elapsed >= warningThreshold) {
                timeClass = 'timeout-warning';
            }

            charGenBatchProgressTime.className = `progress-time ${timeClass}`;
            charGenBatchProgressTime.textContent = `总耗时: ${elapsed}秒 (超时时间: ${timeoutSeconds}秒)`;
        }, 1000);

        // 初始化进度条
        this.updateCharGenProgress(0, '开始角色批量生成...');
    }

    updateCharGenBatchItemStatus(promptId, status, message) {
        const statusElement = document.getElementById(`charGenBatchStatus_${promptId}`);
        const imagesElement = document.getElementById(`charGenBatchImages_${promptId}`);

        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `batch-scene-status ${status}`;
        }

        if (status === 'success' && imagesElement) {
            // 从消息中提取图片数量
            const match = message.match(/(\d+)张/);
            if (match) {
                imagesElement.textContent = `完成: ${match[1]}张`;
            }
        }
    }

    hideCharGenBatchProgress() {
        const progressContainer = document.getElementById('charGenProgressContainer');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }

        // 清理计时器
        if (this.charGenBatchProgressTimer) {
            clearInterval(this.charGenBatchProgressTimer);
            this.charGenBatchProgressTimer = null;
        }
    }

    // ===== 保存设置相关函数 =====

    async loadSaveSettings() {
        try {
            const response = await this.apiCall('/api/config/save-settings', 'GET');

            // 更新界面
            document.getElementById('preventOverwriteCheck').checked = response.prevent_overwrite !== false;
            document.getElementById('addTimestampCheck').checked = response.add_timestamp !== false;
            document.getElementById('filenameFormatSelect').value = response.filename_format || 'timestamp';

            // 更新预览
            this.updateFilenamePreview();

        } catch (error) {
            console.error('加载保存设置失败:', error);
            // 使用默认值
            document.getElementById('preventOverwriteCheck').checked = true;
            document.getElementById('addTimestampCheck').checked = true;
            document.getElementById('filenameFormatSelect').value = 'timestamp';
            this.updateFilenamePreview();
        }
    }

    async saveSaveSettings() {
        try {
            const settings = {
                prevent_overwrite: document.getElementById('preventOverwriteCheck').checked,
                add_timestamp: document.getElementById('addTimestampCheck').checked,
                filename_format: document.getElementById('filenameFormatSelect').value
            };

            const response = await this.apiCall('/api/config/save-settings', 'POST', settings);

            if (response.success) {
                this.addLog('✅ 保存设置已更新');
                this.showSuccess('保存设置已更新');
            } else {
                this.showError('保存设置失败: ' + (response.error || '未知错误'));
            }

        } catch (error) {
            this.showError('保存设置失败: ' + error.message);
        }
    }

    updateFilenamePreview() {
        const preventOverwrite = document.getElementById('preventOverwriteCheck').checked;
        const addTimestamp = document.getElementById('addTimestampCheck').checked;
        const filenameFormat = document.getElementById('filenameFormatSelect').value;

        let preview = '';
        const now = new Date();
        const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, -5).replace('T', '_');

        if (filenameFormat === 'timestamp' && addTimestamp) {
            preview = `test_1_${timestamp}_1.png`;
        } else if (filenameFormat === 'simple') {
            preview = 'test_1_1.png';
        } else {
            preview = `test_1_${timestamp}_1.png`;
        }

        if (preventOverwrite) {
            preview += ' (如文件存在会自动添加序号)';
        }

        document.getElementById('filenamePreview').textContent = preview;
    }

    showSuccess(message) {
        // 简单的成功提示，可以后续改进为更好的UI
        this.addLog(message);

        // 临时显示成功消息
        const successDiv = document.createElement('div');
        successDiv.className = 'alert alert-success';
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 12px 20px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        `;
        successDiv.textContent = message;

        document.body.appendChild(successDiv);

        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);
    }

    // ===== 图床历史记录相关函数 =====

    async loadUploadHistory() {
        const historyLoading = document.getElementById('historyLoading');
        const historyEmpty = document.getElementById('historyEmpty');
        const historyList = document.getElementById('historyList');

        try {
            // 显示加载状态
            historyLoading.style.display = 'block';
            historyEmpty.style.display = 'none';
            historyList.innerHTML = '';

            const response = await this.apiCall('/api/imagehost/history', 'GET');

            if (response.success && response.history) {
                const history = response.history;

                if (history.length === 0) {
                    // 显示空状态
                    historyLoading.style.display = 'none';
                    historyEmpty.style.display = 'block';
                } else {
                    // 渲染历史记录
                    this.renderUploadHistory(history);
                    historyLoading.style.display = 'none';
                }
            } else {
                throw new Error(response.error || '加载历史记录失败');
            }

        } catch (error) {
            historyLoading.style.display = 'none';
            historyEmpty.style.display = 'block';
            historyEmpty.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <p>加载历史记录失败: ${error.message}</p>
            `;
            this.showError('加载历史记录失败: ' + error.message);
        }
    }

    renderUploadHistory(history) {
        const historyList = document.getElementById('historyList');

        historyList.innerHTML = history.map(item => {
            const uploadTime = new Date(item.upload_time).toLocaleString();
            const fileSize = this.formatFileSize(item.file_size);

            return `
                <div class="history-item" data-id="${item.id}">
                    <div class="history-thumbnail" onclick="app.showSimpleImagePreview('${item.url}')">
                        <img src="${item.url}" alt="${item.original_filename}"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjhGOUZBIi8+CjxwYXRoIGQ9Ik0yNCAzMkM0NC40IDMyIDQ4IDM1LjYgNDggNTZDNDggNzYuNCA0NC40IDgwIDI0IDgwQzMuNiA4MCAyIDc2LjQgMiA1NkMyIDM1LjYgNS42IDMyIDI0IDMyWiIgZmlsbD0iI0U5RUNFRiIvPgo8cGF0aCBkPSJNMzIgNDBIMTZWNTZIMzJWNDBaIiBmaWxsPSIjNkM3NTdEIi8+CjxjaXJjbGUgY3g9IjI0IiBjeT0iNDgiIHI9IjQiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+Cg=='">
                    </div>
                    <div class="history-info">
                        <div class="history-filename">${item.original_filename}</div>
                        <div class="history-details">
                            <div>上传时间: ${uploadTime}</div>
                            <div>文件大小: ${fileSize}</div>
                            <div>文件名: ${item.uploaded_filename}</div>
                        </div>
                        <div class="history-url" onclick="app.copyToClipboard('${item.url}')" title="点击复制链接">
                            ${item.url}
                        </div>
                    </div>
                    <div class="history-actions">
                        <button class="btn btn-outline btn-sm" onclick="app.copyToClipboard('${item.url}')">
                            <i class="fas fa-copy"></i> 复制链接
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="app.deleteHistoryItem('${item.id}')">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    async deleteHistoryItem(itemId) {
        if (!confirm('确定要删除这条历史记录吗？')) {
            return;
        }

        try {
            const response = await this.apiCall(`/api/imagehost/history/${itemId}`, 'DELETE');

            if (response.success) {
                this.addLog('✅ 历史记录已删除');
                // 重新加载历史记录
                this.loadUploadHistory();
            } else {
                this.showError('删除失败: ' + (response.error || '未知错误'));
            }

        } catch (error) {
            this.showError('删除失败: ' + error.message);
        }
    }

    async clearUploadHistory() {
        if (!confirm('确定要清空所有上传历史记录吗？此操作不可恢复！')) {
            return;
        }

        try {
            const response = await this.apiCall('/api/imagehost/history/clear', 'POST');

            if (response.success) {
                this.addLog('✅ 历史记录已清空');
                // 重新加载历史记录
                this.loadUploadHistory();
            } else {
                this.showError('清空失败: ' + (response.error || '未知错误'));
            }

        } catch (error) {
            this.showError('清空失败: ' + error.message);
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    copyToClipboard(text) {
        if (navigator.clipboard && window.isSecureContext) {
            // 使用现代API
            navigator.clipboard.writeText(text).then(() => {
                this.showSuccess('链接已复制到剪贴板');
            }).catch(err => {
                this.fallbackCopyToClipboard(text);
            });
        } else {
            // 降级方案
            this.fallbackCopyToClipboard(text);
        }
    }

    fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            this.showSuccess('链接已复制到剪贴板');
        } catch (err) {
            this.showError('复制失败，请手动复制');
        }

        document.body.removeChild(textArea);
    }

    showSimpleImagePreview(imageUrl) {
        // 创建模态框显示大图
        const modal = document.createElement('div');
        modal.className = 'image-preview-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            cursor: pointer;
        `;

        const img = document.createElement('img');
        img.src = imageUrl;
        img.style.cssText = `
            max-width: 95%;
            max-height: 95%;
            object-fit: contain;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        `;

        // 添加关闭提示
        const closeHint = document.createElement('div');
        closeHint.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 14px;
            background: rgba(0, 0, 0, 0.6);
            padding: 8px 12px;
            border-radius: 6px;
            pointer-events: none;
        `;
        closeHint.textContent = '点击任意位置或按ESC关闭';

        modal.appendChild(img);
        modal.appendChild(closeHint);
        document.body.appendChild(modal);

        // 点击关闭
        modal.addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        // ESC键关闭
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                document.body.removeChild(modal);
                document.removeEventListener('keydown', handleKeyDown);
            }
        };
        document.addEventListener('keydown', handleKeyDown);
    }

    async showImageSelector() {
        try {
            // 获取图床历史记录
            const response = await fetch('/api/imagehost/history');
            if (!response.ok) {
                throw new Error('获取图床历史失败');
            }

            const data = await response.json();

            // 检查API响应格式
            if (!data.success) {
                throw new Error(data.error || '获取图床历史失败');
            }

            const history = data.history || [];

            // 创建图片选择器模态框
            const modal = document.createElement('div');
            modal.className = 'image-selector-modal';

            modal.innerHTML = `
                <div class="image-selector-content">
                    <div class="image-selector-header">
                        <h3><i class="fas fa-images"></i> 选择图片</h3>
                        <button class="image-selector-close" onclick="this.closest('.image-selector-modal').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="image-selector-body">
                        ${history.length > 0 ?
                            `<div class="image-grid">
                                ${history.map(item => `
                                    <div class="image-grid-item" data-url="${item.url}" onclick="app.selectImage(this, '${item.url}')">
                                        <div class="image-grid-thumbnail">
                                            <img src="${item.url}" alt="${item.original_filename}"
                                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjhGOUZBIi8+CjxwYXRoIGQ9Ik0yNCAzMkM0NC40IDMyIDQ4IDM1LjYgNDggNTZDNDggNzYuNCA0NC40IDgwIDI0IDgwQzMuNiA4MCAyIDc2LjQgMiA1NkMyIDM1LjYgNS42IDMyIDI0IDMyWiIgZmlsbD0iI0U5RUNFRiIvPgo8cGF0aCBkPSJNMzIgNDBIMTZWNTZIMzJWNDBaIiBmaWxsPSIjNkM3NTdEIi8+CjxjaXJjbGUgY3g9IjI0IiBjeT0iNDgiIHI9IjQiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+Cg=='">
                                        </div>
                                        <div class="image-grid-info">
                                            <div class="image-grid-filename">${item.original_filename}</div>
                                            <div>${new Date(item.upload_time).toLocaleDateString()}</div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>` :
                            `<div style="text-align: center; padding: 40px; color: #6c757d;">
                                <i class="fas fa-images" style="font-size: 3rem; margin-bottom: 16px; opacity: 0.5;"></i>
                                <p style="font-size: 1.1rem; margin-bottom: 8px;">暂无上传历史</p>
                                <p style="font-size: 0.9rem;">请先使用图床功能上传图片</p>
                            </div>`
                        }
                    </div>
                    <div class="image-selector-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.image-selector-modal').remove()">
                            取消
                        </button>
                        <button class="btn btn-primary" id="confirmSelectBtn" onclick="app.confirmImageSelection()" disabled>
                            确认选择
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });

            // ESC键关闭
            const handleKeyDown = (e) => {
                if (e.key === 'Escape') {
                    modal.remove();
                    document.removeEventListener('keydown', handleKeyDown);
                }
            };
            document.addEventListener('keydown', handleKeyDown);

        } catch (error) {
            this.showError('获取图床历史失败: ' + error.message);
        }
    }

    selectImage(element, url) {
        // 移除其他选中状态
        const allItems = element.parentElement.querySelectorAll('.image-grid-item');
        allItems.forEach(item => item.classList.remove('selected'));

        // 添加选中状态
        element.classList.add('selected');

        // 启用确认按钮
        const confirmBtn = document.getElementById('confirmSelectBtn');
        if (confirmBtn) {
            confirmBtn.disabled = false;
            confirmBtn.setAttribute('data-selected-url', url);
        }
    }

    confirmImageSelection() {
        const confirmBtn = document.getElementById('confirmSelectBtn');
        const selectedUrl = confirmBtn?.getAttribute('data-selected-url');

        if (selectedUrl) {
            // 将选中的URL填入输入框
            const urlInput = document.getElementById('outfitImageUrl');
            if (urlInput) {
                urlInput.value = selectedUrl;

                // 触发input事件，以便其他监听器能够响应
                urlInput.dispatchEvent(new Event('input', { bubbles: true }));
                urlInput.dispatchEvent(new Event('change', { bubbles: true }));
            }

            // 关闭模态框
            const modal = document.querySelector('.image-selector-modal');
            if (modal) {
                modal.remove();
            }

            this.addLog('已选择图片: ' + selectedUrl.split('/').pop());
        }
    }

    bindImagePreviewEvents() {
        const urlInput = document.getElementById('outfitImageUrl');
        const previewContainer = document.getElementById('imagePreviewContainer');
        const previewImg = document.getElementById('imagePreviewImg');
        const previewPlaceholder = document.getElementById('imagePreviewPlaceholder');

        if (!urlInput || !previewContainer || !previewImg || !previewPlaceholder) {
            return;
        }

        // 实时预览图片
        const updatePreview = () => {
            const url = urlInput.value.trim();

            if (url && this.isValidImageUrl(url)) {
                previewImg.src = url;
                previewImg.onclick = () => this.showSimpleImagePreview(url);
                previewContainer.style.display = 'block';
                previewPlaceholder.style.display = 'none';
            } else {
                previewContainer.style.display = 'none';
                previewPlaceholder.style.display = 'block';
            }
        };

        // 绑定输入事件
        urlInput.addEventListener('input', updatePreview);
        urlInput.addEventListener('change', updatePreview);
        urlInput.addEventListener('paste', () => {
            // 延迟执行，确保粘贴的内容已经填入
            setTimeout(updatePreview, 100);
        });

        // 初始化预览
        updatePreview();
    }

    isValidImageUrl(url) {
        try {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname.toLowerCase();
            const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];

            // 检查URL是否以图片扩展名结尾，或者是常见的图床域名
            const hasImageExtension = imageExtensions.some(ext => pathname.endsWith(ext));
            const isImageHost = ['imgur.com', 'i.imgur.com', 'github.com', 'githubusercontent.com',
                               'cloudinary.com', 'unsplash.com', 'pexels.com'].some(host =>
                               urlObj.hostname.includes(host));

            return hasImageExtension || isImageHost || url.includes('placeholder') || url.includes('via.placeholder');
        } catch {
            return false;
        }
    }

    bindCharacterCodeValidation() {
        const codeInput = document.getElementById('characterCode');
        const validationMessage = document.getElementById('codeValidationMessage');

        if (!codeInput || !validationMessage) {
            return;
        }

        let validationTimeout;

        const validateCode = async () => {
            const code = codeInput.value.trim();
            const originalCode = codeInput.getAttribute('data-original-code') || '';

            // 清除之前的样式
            codeInput.classList.remove('valid', 'invalid', 'checking');
            validationMessage.style.display = 'none';

            if (!code) {
                return;
            }

            // 基本格式验证
            if (!this.isValidCharacterCode(code)) {
                this.showCodeValidation('error', '角色编码长度必须在2-50个字符之间', codeInput, validationMessage);
                return;
            }

            // 如果是编辑模式且编码未改变，直接通过
            if (originalCode && code === originalCode) {
                this.showCodeValidation('success', '角色编码有效', codeInput, validationMessage);
                return;
            }

            // 显示检查状态
            codeInput.classList.add('checking');
            this.showCodeValidation('warning', '正在检查编码是否重复...', codeInput, validationMessage);

            try {
                // 检查是否重复
                const isDuplicate = await this.checkCharacterCodeDuplicate(code, originalCode);

                if (isDuplicate) {
                    this.showCodeValidation('error', '角色编码已存在，请使用其他编码', codeInput, validationMessage);
                } else {
                    this.showCodeValidation('success', '角色编码可用', codeInput, validationMessage);
                }
            } catch (error) {
                this.showCodeValidation('warning', '无法验证编码重复性，请手动确认', codeInput, validationMessage);
            }
        };

        // 绑定输入事件，使用防抖
        codeInput.addEventListener('input', () => {
            clearTimeout(validationTimeout);
            validationTimeout = setTimeout(validateCode, 500);
        });

        codeInput.addEventListener('blur', validateCode);

        // 初始验证
        if (codeInput.value.trim()) {
            validateCode();
        }
    }

    isValidCharacterCode(code) {
        // 角色编码只需要满足长度要求，不限制字符类型
        return code.length >= 2 && code.length <= 50;
    }

    async checkCharacterCodeDuplicate(code, originalCode = '') {
        try {
            // 获取所有角色
            const response = await fetch('/api/config/characters');
            if (!response.ok) {
                throw new Error('获取角色列表失败');
            }

            const characters = await response.json();

            // 检查是否有重复的编码（排除原始编码）
            return characters.some(char =>
                char.character_code === code && char.character_code !== originalCode
            );
        } catch (error) {
            console.error('检查角色编码重复性失败:', error);
            throw error;
        }
    }

    showCodeValidation(type, message, input, messageElement) {
        // 清除之前的样式
        input.classList.remove('valid', 'invalid', 'checking');
        messageElement.classList.remove('success', 'error', 'warning');

        // 添加新样式
        if (type === 'success') {
            input.classList.add('valid');
            messageElement.classList.add('success');
            messageElement.innerHTML = '<i class="fas fa-check-circle"></i>' + message;
        } else if (type === 'error') {
            input.classList.add('invalid');
            messageElement.classList.add('error');
            messageElement.innerHTML = '<i class="fas fa-exclamation-circle"></i>' + message;
        } else if (type === 'warning') {
            input.classList.add('checking');
            messageElement.classList.add('warning');
            messageElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i>' + message;
        }

        messageElement.style.display = 'flex';
    }

    // ===== 服装管理方法 =====

    addOutfit(characterCode) {
        const character = this.characters?.find(c => c.character_code === characterCode);
        if (!character) {
            this.showError('角色不存在');
            return;
        }

        this.currentEditingCharacter = character;
        this.currentEditingOutfit = null;
        this.showModal('添加服装', this.createOutfitFormHTML());
        this.bindModalEvents('outfit');
        this.bindImagePreviewEvents();
    }

    editOutfit(characterCode, outfitId) {
        const character = this.characters?.find(c => c.character_code === characterCode);
        if (!character) {
            this.showError('角色不存在');
            return;
        }

        const outfit = character.outfits?.find(o => o.outfit_id === outfitId);
        if (!outfit) {
            this.showError('服装不存在');
            return;
        }

        this.currentEditingCharacter = character;
        this.currentEditingOutfit = outfit;
        this.showModal('编辑服装', this.createOutfitFormHTML(outfit));
        this.bindModalEvents('outfit');
        this.bindImagePreviewEvents();
    }

    async deleteOutfit(characterCode, outfitId) {
        const character = this.characters?.find(c => c.character_code === characterCode);
        if (!character) {
            this.showError('角色不存在');
            return;
        }

        const outfit = character.outfits?.find(o => o.outfit_id === outfitId);
        if (!outfit) {
            this.showError('服装不存在');
            return;
        }

        if (!confirm(`确定要删除服装"${outfit.outfit_name}"吗？`)) {
            return;
        }

        try {
            // 从角色的服装列表中移除
            character.outfits = character.outfits.filter(o => o.outfit_id !== outfitId);
            character.updated_at = new Date().toISOString();

            await this.apiCall('/api/config/characters', 'POST', this.characters);

            this.addLog(`删除服装"${outfit.outfit_name}"成功`);
            this.loadCharacters();
        } catch (error) {
            this.showError('删除服装失败: ' + error.message);
        }
    }

    createOutfitFormHTML(outfit = null) {
        const isEdit = outfit !== null;

        return `
            <form id="outfitForm" class="modern-form">
                <div class="form-header">
                    <h3>${isEdit ? '编辑服装' : '添加新服装'}</h3>
                    <p class="form-subtitle">为角色配置服装信息和参考图片</p>
                </div>

                <div class="form-body">
                    <div class="form-group">
                        <label for="outfitName" class="form-label">
                            <i class="fas fa-tag"></i>
                            服装名称 <span class="required">*</span>
                        </label>
                        <input type="text" id="outfitName" name="outfit_name"
                               class="form-input"
                               value="${outfit?.outfit_name || ''}"
                               placeholder="例如：日常休闲装、正式礼服等" required>
                    </div>

                    <div class="form-group">
                        <label for="outfitDescription" class="form-label">
                            <i class="fas fa-align-left"></i>
                            服装描述
                        </label>
                        <textarea id="outfitDescription" name="outfit_description"
                                  class="form-textarea"
                                  rows="3"
                                  placeholder="描述服装的特点、风格、颜色等（可选）">${outfit?.outfit_description || ''}</textarea>
                    </div>

                    <div class="form-group">
                        <label for="outfitImageUrl" class="form-label">
                            <i class="fas fa-image"></i>
                            服装图片链接 <span class="required">*</span>
                        </label>
                        <div class="image-url-input-group">
                            <input type="url" id="outfitImageUrl" name="image_url"
                                   class="form-input"
                                   value="${outfit?.image_url || ''}"
                                   placeholder="https://example.com/outfit.jpg" required>
                            <button type="button" class="btn btn-secondary image-select-btn" onclick="app.showImageSelector()" title="从图床历史中选择">
                                <i class="fas fa-folder-open"></i>
                                选择图片
                            </button>
                        </div>
                        <div class="form-help">
                            <i class="fas fa-info-circle"></i>
                            可以手动输入链接，或点击"选择图片"从图床历史记录中选择
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-eye"></i>
                            图片预览
                        </label>
                        <div id="imagePreviewContainer" class="image-preview-container" style="display: ${outfit?.image_url ? 'block' : 'none'}">
                            <img id="imagePreviewImg" src="${outfit?.image_url || ''}" alt="服装预览" class="preview-image" onclick="app.showSimpleImagePreview(this.src)" title="点击查看大图">
                            <div class="preview-overlay">
                                <i class="fas fa-search-plus"></i>
                                点击查看大图
                            </div>
                        </div>
                        <div id="imagePreviewPlaceholder" style="display: ${outfit?.image_url ? 'none' : 'block'}; text-align: center; padding: 40px; color: #6c757d; border: 2px dashed #dee2e6; border-radius: 8px;">
                            <i class="fas fa-image" style="font-size: 2rem; margin-bottom: 8px; opacity: 0.5;"></i>
                            <p style="margin: 0; font-size: 0.9rem;">输入图片链接后将显示预览</p>
                        </div>
                    </div>
                </div>
            </form>
        `;
    }

    async saveOutfit() {
        const form = document.getElementById('outfitForm');
        const formData = new FormData(form);

        const outfitData = {
            outfit_name: formData.get('outfit_name'),
            outfit_description: formData.get('outfit_description'),
            image_url: formData.get('image_url')
        };

        // 验证必填字段
        if (!outfitData.outfit_name || !outfitData.image_url) {
            this.showError('请填写服装名称和图片链接');
            return;
        }

        try {
            const character = this.currentEditingCharacter;

            if (this.currentEditingOutfit) {
                // 编辑现有服装
                const outfit = this.currentEditingOutfit;
                outfit.outfit_name = outfitData.outfit_name;
                outfit.outfit_description = outfitData.outfit_description;
                outfit.image_url = outfitData.image_url;
            } else {
                // 添加新服装
                const newOutfitId = `outfit_${Date.now()}`;
                const newOutfit = {
                    outfit_id: newOutfitId,
                    outfit_name: outfitData.outfit_name,
                    outfit_description: outfitData.outfit_description,
                    image_url: outfitData.image_url,
                    created_at: new Date().toISOString()
                };

                if (!character.outfits) {
                    character.outfits = [];
                }
                character.outfits.push(newOutfit);
            }

            character.updated_at = new Date().toISOString();

            await this.apiCall('/api/config/characters', 'POST', this.characters);

            this.addLog(`${this.currentEditingOutfit ? '编辑' : '添加'}服装成功`);
            this.closeModal();
            this.loadCharacters();
        } catch (error) {
            this.showError(`${this.currentEditingOutfit ? '编辑' : '添加'}服装失败: ` + error.message);
        }
    }

    // Excel编辑器相关方法
    async handleExcelEditorUpload(file) {
        if (!file) return;

        // 检查文件类型
        const allowedTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];

        if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
            this.showError('请选择有效的Excel文件 (.xlsx 或 .xls)');
            return;
        }

        this.showExcelEditorProgress('正在读取Excel文件...');

        try {
            const formData = new FormData();
            formData.append('file', file);

            const result = await this.apiCall('/api/excel-editor/upload', 'POST', formData);

            if (result.success) {
                this.excelEditorData = result;
                this.originalExcelData = JSON.parse(JSON.stringify(result)); // 深拷贝
                this.excelEditorFileName = result.filename;

                this.renderExcelEditor();
                this.hideExcelEditorProgress();
                this.showExcelEditorContainer();

                this.addLog(`Excel文件加载成功: ${result.filename} (${result.row_count}行 x ${result.column_count}列)`);
            } else {
                throw new Error(result.error || '文件上传失败');
            }

        } catch (error) {
            this.hideExcelEditorProgress();
            this.showError('Excel文件上传失败: ' + error.message);
        }
    }

    renderExcelEditor() {
        if (!this.excelEditorData) return;

        const { columns, rows, filename, row_count, column_count } = this.excelEditorData;

        // 更新文件信息
        document.getElementById('excelEditorFileName').textContent = filename;
        document.getElementById('excelEditorStats').textContent = `${row_count} 行 × ${column_count} 列`;

        // 渲染表格
        const tableHead = document.getElementById('excelTableHead');
        const tableBody = document.getElementById('excelTableBody');

        // 清空现有内容
        tableHead.innerHTML = '';
        tableBody.innerHTML = '';

        // 创建表头
        const headerRow = document.createElement('tr');

        // 添加行号列头
        const rowNumberHeader = document.createElement('th');
        rowNumberHeader.className = 'row-number';
        rowNumberHeader.textContent = '#';
        headerRow.appendChild(rowNumberHeader);

        // 添加数据列头
        columns.forEach((column, index) => {
            const th = document.createElement('th');
            th.textContent = column;

            // 设置列宽 - 最后一列自动铺满
            if (index === columns.length - 1) {
                // 最后一列自动铺满剩余空间
                th.style.width = 'auto';
                th.style.minWidth = '200px';
            } else {
                const width = this.getColumnWidth(column);
                th.style.width = width;
                th.style.minWidth = width;
            }

            // 标记不同类型的列
            const lowerName = column.toLowerCase();
            if (lowerName.includes('prompt') || column.includes('提示词') ||
                column.includes('文生图') || lowerName.includes('生图')) {
                th.classList.add('prompt-column');
            } else if (column.includes('分镜序号') || column.includes('镜头序号') ||
                       column.includes('序号') || column.includes('编号')) {
                th.classList.add('sequence-column');
            }

            headerRow.appendChild(th);
        });

        tableHead.appendChild(headerRow);

        // 创建表格内容
        rows.forEach((row, rowIndex) => {
            const tr = document.createElement('tr');

            // 添加行号
            const rowNumberCell = document.createElement('td');
            rowNumberCell.className = 'row-number';
            rowNumberCell.textContent = rowIndex + 1;
            tr.appendChild(rowNumberCell);

            // 添加数据单元格
            columns.forEach((column, colIndex) => {
                const td = document.createElement('td');
                const cellValue = row[colIndex] || '';

                // 创建可编辑的文本域
                const textarea = document.createElement('textarea');
                textarea.className = 'excel-cell-input';
                textarea.value = cellValue;
                textarea.rows = 1;

                // 标记不同类型的列并设置相应样式
                const lowerName = column.toLowerCase();
                if (lowerName.includes('prompt') || column.includes('提示词') ||
                    column.includes('文生图') || lowerName.includes('生图')) {
                    td.classList.add('prompt-column');
                    textarea.rows = 4;
                    textarea.style.minHeight = '120px';
                } else if (column.includes('分镜序号') || column.includes('镜头序号') ||
                           column.includes('序号') || column.includes('编号')) {
                    td.classList.add('sequence-column');
                    textarea.style.textAlign = 'center';
                    textarea.style.minHeight = '30px';
                }

                // 设置单元格宽度与列头一致
                if (colIndex === columns.length - 1) {
                    // 最后一列自动铺满
                    td.style.width = 'auto';
                    td.style.minWidth = '200px';
                } else {
                    const width = this.getColumnWidth(column);
                    td.style.width = width;
                    td.style.minWidth = width;
                }

                // 自动调整高度
                this.autoResizeTextarea(textarea);

                // 绑定事件
                textarea.addEventListener('input', (e) => {
                    this.updateCellValue(rowIndex, colIndex, e.target.value);
                    this.autoResizeTextarea(e.target);
                });

                textarea.addEventListener('focus', (e) => {
                    e.target.select();
                });

                td.appendChild(textarea);
                tr.appendChild(td);
            });

            // 移除行高调整功能

            tableBody.appendChild(tr);
        });
    }

    getColumnWidth(columnName) {
        // 根据列名确定初始宽度
        const lowerName = columnName.toLowerCase();

        // 分镜序号列 - 最小宽度
        if (columnName.includes('分镜序号') || columnName.includes('镜头序号') ||
            columnName.includes('序号') || columnName.includes('编号')) {
            return '80px';
        }
        // 其他列使用默认宽度，最后一列会自动铺满
        else {
            return '200px';
        }
    }

    // 移除了列宽和行高调整功能，简化界面操作

    autoResizeTextarea(textarea) {
        if (!textarea) return;

        textarea.style.height = 'auto';
        const parentTd = textarea.closest('td');
        let minHeight = 40;

        if (parentTd && parentTd.classList.contains('prompt-column')) {
            minHeight = 120;
        } else if (parentTd && parentTd.classList.contains('sequence-column')) {
            minHeight = 30;
        }

        const newHeight = Math.max(textarea.scrollHeight, minHeight);
        textarea.style.height = newHeight + 'px';
    }

    updateCellValue(rowIndex, colIndex, value) {
        if (this.excelEditorData && this.excelEditorData.rows[rowIndex]) {
            this.excelEditorData.rows[rowIndex][colIndex] = value;
        }
    }

    showExcelEditorProgress(message) {
        const progressContainer = document.getElementById('excelEditorProgress');
        const progressText = document.getElementById('excelEditorProgressText');
        const progressFill = document.getElementById('excelEditorProgressFill');

        progressText.textContent = message;
        progressFill.style.width = '50%';
        progressContainer.style.display = 'block';
    }

    hideExcelEditorProgress() {
        document.getElementById('excelEditorProgress').style.display = 'none';
    }

    showExcelEditorContainer() {
        document.getElementById('excelEditorContainer').style.display = 'block';
        document.getElementById('exportEditedExcelBtn').style.display = 'inline-block';
        document.getElementById('clearExcelEditorBtn').style.display = 'inline-block';
    }

    hideExcelEditorContainer() {
        document.getElementById('excelEditorContainer').style.display = 'none';
        document.getElementById('exportEditedExcelBtn').style.display = 'none';
        document.getElementById('clearExcelEditorBtn').style.display = 'none';
    }

    async exportEditedExcel() {
        if (!this.excelEditorData) {
            this.showError('没有可导出的数据');
            return;
        }

        try {
            this.showLoading('正在导出Excel文件...');

            const exportData = {
                filename: this.excelEditorFileName,
                excel_data: {
                    columns: this.excelEditorData.columns,
                    rows: this.excelEditorData.rows
                }
            };

            const result = await this.apiCall('/api/excel-editor/save', 'POST', exportData);

            if (result.success) {
                // 下载文件
                const downloadData = {
                    file_path: result.file_path
                };

                const response = await fetch('/api/excel-editor/download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(downloadData)
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = result.filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    this.addLog(`Excel文件导出成功: ${result.filename}`);
                    this.showSuccess('Excel文件导出成功');
                } else {
                    throw new Error('文件下载失败');
                }
            } else {
                throw new Error(result.error || '导出失败');
            }

        } catch (error) {
            this.showError('Excel文件导出失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    clearExcelEditor() {
        if (confirm('确定要清空Excel编辑器的数据吗？此操作不可撤销。')) {
            this.excelEditorData = null;
            this.originalExcelData = null;
            this.excelEditorFileName = '';

            // 清空界面
            document.getElementById('excelTableHead').innerHTML = '';
            document.getElementById('excelTableBody').innerHTML = '';
            document.getElementById('excelEditorFileName').textContent = '';
            document.getElementById('excelEditorStats').textContent = '';

            this.hideExcelEditorContainer();

            this.addLog('Excel编辑器数据已清空');
        }
    }

    // 辅助方法：检查数据是否有变化
    hasExcelEditorChanges() {
        if (!this.excelEditorData || !this.originalExcelData) {
            return false;
        }

        return JSON.stringify(this.excelEditorData.rows) !== JSON.stringify(this.originalExcelData.rows);
    }

    // 在切换标签页时检查是否有未保存的更改
    checkUnsavedExcelChanges() {
        if (this.hasExcelEditorChanges()) {
            return confirm('Excel编辑器中有未保存的更改，确定要离开吗？');
        }
        return true;
    }
}

// 初始化应用
const app = new App();