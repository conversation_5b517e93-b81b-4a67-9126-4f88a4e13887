{"characters": [{"character_code": "<PERSON><PERSON>", "character_name": "咖啡女", "core_features": "1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\n3. 芭蕾舞者般的身形，优雅修长，腰肢纤细，曲线玲珑有致。\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。", "created_at": "2025-07-29T09:58:47.299399", "display_type": "code", "outfits": [{"created_at": "2025-07-29T09:58:47.299422", "image_url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250728_112948.png", "outfit_description": "", "outfit_id": "outfit_1", "outfit_name": "黑色短裙"}], "updated_at": "2025-07-29T03:07:10.225Z"}, {"character_code": "<PERSON><PERSON> Tung <PERSON>hur", "character_name": "木头人", "core_features": "1. 它的整个躯干和头部是一根光滑的木质棒球棒，球棒上部直接雕刻出眼睛、鼻子、嘴巴\n2. 右手总是拿着木质棒球棒", "created_at": "2025-07-29T09:58:47.299433", "display_type": "code", "outfits": [{"created_at": "2025-07-29T09:58:47.299436", "image_url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250728_172228.png", "outfit_description": "", "outfit_id": "outfit_1", "outfit_name": "帅气马甲衬衫"}], "updated_at": "2025-07-29T03:21:48.593Z"}, {"character_code": "<PERSON><PERSON>", "character_name": "小咖啡女", "core_features": "1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\n3. 角色年龄是1~3岁的婴儿。\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。", "created_at": "2025-07-29T09:58:47.299439", "display_type": "code", "outfits": [{"created_at": "2025-07-29T09:58:47.299442", "image_url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_094407.png", "outfit_description": "", "outfit_id": "outfit_1", "outfit_name": "恐龙"}], "updated_at": "2025-07-29T03:21:25.857Z"}, {"character_code": "Iron Doctor", "character_name": "钢铁侠", "core_features": "", "created_at": "2025-07-29T14:08:50.951Z", "display_type": "code", "outfits": [{"created_at": "2025-07-29T14:09:23.869Z", "image_url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_220902.png", "outfit_description": "", "outfit_id": "outfit_1753798163868", "outfit_name": "医生"}, {"created_at": "2025-07-30T06:27:11.294Z", "image_url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250730_112137.png", "outfit_description": "", "outfit_id": "outfit_1753856831294", "outfit_name": "警察"}, {"outfit_id": "outfit_1753856864850", "outfit_name": "警车", "outfit_description": "", "image_url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250730_142728.png", "created_at": "2025-07-30T06:27:44.850Z"}], "updated_at": "2025-07-30T06:27:44.850Z"}, {"character_code": "<PERSON><PERSON><PERSON>", "character_name": "咖啡男", "core_features": "1. 一位拟人化的咖啡刺客角色。它的身体是一个卡布奇诺纸杯，上面为黑色咖啡杯杯盖。杯身正面有一双巨大的、带有杀气的眼睛。 \n2. 额头戴着金属感忍者护额，标志着其刺客身份。", "created_at": "2025-07-29T03:24:30.844Z", "display_type": "code", "outfits": [{"created_at": "2025-07-29T03:33:37.454Z", "image_url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_112218.png", "outfit_description": "", "outfit_id": "outfit_1753760017454", "outfit_name": "警察"}], "updated_at": "2025-07-29T03:33:37.454Z"}, {"character_code": "<PERSON><PERSON><PERSON>", "character_name": "大象", "core_features": "1.头是大象的头，有大象的鼻子、耳朵、象牙等。 \n2.身体仙人掌：除了头之外，都是由仙人掌构成，比如手、脚、身体躯干，都是仙人掌。", "created_at": "2025-07-29T08:20:07.859Z", "display_type": "name", "outfits": [{"created_at": "2025-07-29T08:20:52.504Z", "image_url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_162029.png", "outfit_description": "", "outfit_id": "outfit_1753777252504", "outfit_name": "医生"}], "updated_at": "2025-07-29T09:42:13.721Z"}], "default_model": null, "default_service": "1", "imagehost": {"github_token": "****************************************", "max_file_size": 3, "repo_path": "/scys-wcs/photo/contents/image"}, "model_services": [{"api_key": "sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY", "api_url": "https://yunwu.ai/v1/chat/completions", "id": 1, "models": [{"name": "sora_image", "type": "image"}, {"name": "gpt-image-1-all", "type": "image"}], "name": "云雾api", "retry_count": 3, "timeout": 600}, {"api_key": "eVw5BVFb4TFp6", "api_url": "https://ai.liaobots.work/v1/chat/completions", "id": 2, "models": [{"name": "sora_image", "type": "image"}], "name": "LiaoBot", "retry_count": 3, "timeout": 180}], "settings": {"retry_count": 3, "save_directory": "D:\\BaiduSyncdisk\\Youtube\\00待剪辑", "timeout": 180}}